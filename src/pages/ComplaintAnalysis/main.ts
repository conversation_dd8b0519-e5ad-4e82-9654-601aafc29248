import { createApp } from "vue";
import piniaEntity from "@/stores";

import App from "./App.vue";
import router from "./router";
import "vant/lib/index.css";
import "@/assets/main.less";
import "jb-mobile-ui/dist/index.css";
import "./base.css";
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
import { vLoading } from "@/directives/index.ts";
import VirtualScroller from 'vue-virtual-scroller'
import { Loading } from 'vant';
const app = createApp(App);
app.use(VirtualScroller)
app.use(piniaEntity);
app.use(router);
app.use(Loading);
app.directive("loading", vLoading);

app.mount("#app");

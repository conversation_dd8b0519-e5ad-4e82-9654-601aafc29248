import { createRouter, createWebHistory } from "vue-router";
import { setupRouterGuard } from "@/hooks/useRouterGuard";

const router = createRouter({
  history: createWebHistory("/ComplaintAnalysis"),
  routes: [
    {
      path: "/",
      redirect: "/event",
      component: () => import("../views/Layout.vue"),
      children: [
        {
          name: "index",
          path: "/index",
          component: () => import("../views/Index.vue"),
        },
        {
          name: "chart",
          path: "/chart",
          component: () => import("../views/Chart.vue"),
        },
        {
          name: "rank",
          path: "/rank",
          component: () => import("../views/Rank.vue"),
        },
        {
          name: "event",
          path: "/event",
          component: () => import("../views/Event.vue"),
        },
        {
          name: "topic",
          path: "/topic",
          component: () => import("../views/Topic.vue"),
        },
        {
          name: "overview",
          path: "/overview",
          component: () => import("../views/Overview.vue"),
        },
      ],
    },
    {
      name: "dialog",
      path: "/dialog",
      component: () => import("../views/Dialog.vue"),
    },
    {
      name: "report",
      path: "/report",
      component: () => import("../views/Report.vue"),
    },
    {
      name: "bind",
      path: "/bind",
      component: () => import("../views/Bind.vue"),
    },
    {
      name: "result",
      path: "/result",
      component: () => import("../views/Result.vue"),
    },
  ],
});
// 使用公共路由守卫
setupRouterGuard(router);
export default router;

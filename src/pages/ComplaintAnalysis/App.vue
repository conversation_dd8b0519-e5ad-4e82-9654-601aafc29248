<script setup lang="ts">
import { watch } from 'vue'
import { RouterView } from 'vue-router'
import { storeToRefs } from 'pinia'
import {getWecomTicket} from "@/api/wecom.ts";
import {onMounted} from "vue";
import {init, createWatermark} from "@/utils/common.ts";
import { useUserStore } from '@/stores/user'
const userStore = useUserStore()
const { userInfo, userSuffix } = storeToRefs(userStore) || {}
watch(() => userInfo.value, () => {
  if (userInfo.value?.userName) {
    createWatermark(`${userInfo.value.userName}${userSuffix.value}`)
  }
}, { immediate: true })
const getJsapiTicket = async (data: unknown) => {
  const res = await getWecomTicket(data)
  const {code, data: ticket} = res?.data || {}
  if (code === '00000') {
    await init({jsapiTicket: ticket})
  }
}
onMounted(async () => {
  const {VITE_APP_CORP_ID, VITE_APP_WECOM_SECRET} = import.meta.env;
  // await getJsapiTicket({corpId: VITE_APP_CORP_ID, secret: VITE_APP_WECOM_SECRET})
})
</script>

<template>
  <div class="outer-container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>

</template>

<style scoped lang="less">

</style>

<script setup lang="ts">
import {nextTick, effect, reactive, ref, computed, watch} from "vue";
import ChatBubble from "@/components/ChatBubble.vue";
import {getGroupChatRecord} from "@/api/wecom.ts";
// @ts-ignore
import { DynamicScroller, DynamicScrollerItem } from 'vue-virtual-scroller';
import { useInfiniteScroll, useGlobalLoading, JbMobileSelect, JbMobileEmpty } from "jb-mobile-ui";
import dayjs from "dayjs";
import {useEventStore} from "@/stores/event.ts";
import {storeToRefs} from "pinia";
interface IGroup {
  groupName: string;
  wechatGroupId: string;
  houseId: string;
  startTime: string;
  endTime: string;
}
interface IPageInfo {
  currentPage: number; // 当前页码
  pageSize: number; // 每页数量
  timeType: 0 | 1; // 时间类型 0 只赋值开始时间--正序   1 只赋值结束时间--倒序
  startTime: number; // 开始时间
  endTime: number; // 结束时间
  wechatGroupId?: string; // 群id
  houseId?: string; // 项目id
}
let firstFlag = true
/**
 * 当前群信息
 * */
const currentGroup = reactive<IGroup>({
  groupName: "",
  wechatGroupId: "",
  houseId: "",
  startTime: "",
  endTime: ""
})
const selectRef = ref() // 选择器实例
const globalLoading = useGlobalLoading()
const eventStore = useEventStore()
const {dialogHouseId, dialogGroupList} = storeToRefs(eventStore) || {}
// 聊天记录列表
const dialogueList = ref<any[]>([])
watch(dialogGroupList, (val) => {
  nextTick(() => {
    if (Array.isArray(val) && val?.length > 0) {
      const newItem = val[0]
      const {wechatGroupId, wechatGroupName, startTime, endTime} = newItem || {}
      selectRef.value?.handleSelectOptionItem?.(newItem)
      currentGroup.houseId = dialogHouseId.value
      currentGroup.wechatGroupId = wechatGroupId
      currentGroup.groupName = wechatGroupName
      currentGroup.startTime = startTime && dayjs(startTime).valueOf()
      currentGroup.endTime = endTime && dayjs(endTime).valueOf()
      // forceUpdateKey.value++
    } else {
      dialogueList.value = []
    }
  })
}, {immediate: true})
watch(currentGroup, (val) => {
  previousPageInfo.currentPage = 1
  pageInfo.currentPage = 1
  getList()
  // loadMoreData()
})
const selectedGroupName = computed(() => dialogGroupList.value?.find(item => item.wechatGroupId === currentGroup.wechatGroupId)?.wechatGroupName)
const dialogueContentRef = ref() // 虚拟滚动组件实例
const forceUpdateKey = ref(0)
const fieldNames = reactive({
  value: "wechatGroupId",
  label: "wechatGroupName"
})
const previousPageInfo = reactive<IPageInfo>({
  currentPage: 1,
  pageSize: 20,
  timeType: 1,
  startTime: 0,
  endTime: 0
})
/**
 * 群消息页码
 * */
const pageInfo = reactive<IPageInfo>({
  currentPage: 1,
  pageSize: 20,
  timeType: 0,
  startTime: 0,
  endTime: 0
})
/**
 * 当有数据添加到数组，滚动条依然停留在当前阅读位置
 * */
const keepStay = async (result: Array<unknown>) => {
  // 记录当前用户正在查看的元素索引
  const scrollElement = dialogueContentRef.value?.$el // 当前虚拟组件真是元素
  const currentScrollTop = scrollElement?.scrollTop || 0 // 当前滚动条位置

  // 估算当前查看的元素索引
  const estimatedCurrentIndex = Math.floor(currentScrollTop / 60)
  // 滚动到原来查看的元素（现在的索引是 originalIndex + result.length）
  const targetIndex = estimatedCurrentIndex + result.length
  const virtualScroller = dialogueContentRef.value

  if (virtualScroller && virtualScroller.scrollToItem) {
    virtualScroller?.scrollToItem(targetIndex)
  } else if (scrollElement) {
    scrollElement.scrollTop = targetIndex * 60
  }

}
const showMoreMsg = ref(false) // 是否显示更多聊天记录按钮
/**
* 查看是否存在上一页数据
* */
const checkPreviousList = async (pageInfo: IPageInfo) => {
  return await getGroupChatRecord({
    ...pageInfo, ...currentGroup
  })
}
/**
 * 点击显示更多聊天记录（查看更早的记录）
 * */
const handleShowMorePrevious = async () => {
  dialogueContentRef.value.$el.scrollTop = 0
  // console.log('el', el)
  showMoreMsg.value = false
  await new Promise(resolve => requestAnimationFrame(async () => {
    await getList(previousPageInfo, 'front')
  }))
  // pageInfo.currentPage += 1
  // await getList(previousPageInfo, 'front')
}
/**
 * 获取列表数据（聊天列表）
 * @param currPageInfo 当前页码信息
 * @param direction 加载方向，默认是向后加载
 * */
const getList = async (currPageInfo?: IPageInfo, direction = 'back') => {
  try {
    globalLoading.value = true
    const response = await getGroupChatRecord({
      ...pageInfo, ...(currPageInfo || {}), ...currentGroup
    })
    const {code, data} = response?.data
    if (code === '00000') {
      const { result } = data || {}
      // dialogueList.value = result || []
      if (result && result.length > 0) {
        // 将新数据追加到现有列表
        if (currPageInfo) {
          // 加载以前的数据
          dialogueList.value = direction === 'front' ? [...result.reverse(), ...dialogueList.value] : [...dialogueList.value, ...result];
          // 强制重新渲染组件
          // forceUpdateKey.value++
          if (direction === 'front') {
            showMoreMsg.value = false
          }
          await nextTick()
          await keepStay(result) // 让滚动条停留在当前位置，没有这个方法滚动条会滚到最上面
          return true
        } else {
          // 正常第一次加载
          dialogueList.value = result || [];
        }
        if (!firstFlag) {
          return
        }
        try {
          const msgTime = result[0]?.msgTime
          previousPageInfo.endTime = dayjs(msgTime).endOf('day').valueOf()
          const pre = await checkPreviousList({
            currentPage: 1,
            pageSize: 20,
            timeType: 1,
            startTime: 0,
            endTime: previousPageInfo.endTime
          })
          const preList = pre?.data?.data?.result
          showMoreMsg.value = preList?.length > 0 // 判断是否存在上一页数据，存在则显示更多按钮
          firstFlag = false
          // console.log('pre', pre.data.data.result)
        } finally {
          globalLoading.value = false
          // setDataState({ hasMore: false })
          // console.log('isLoadingMore', isLoadingMore.value)
        }
      }
      // console.log('result', result)
    }
  } catch (error) {
    console.log('error', error)
    // globalLoading.value = false
  } finally {
    globalLoading.value = false
  }
}

/**
 * 加载更多数据（向下滚动触底时调用）
 */
const loadMoreData = async () => {
  try {
    // 增加页码
    pageInfo.currentPage += 1
    await getList(pageInfo, 'back')
    // const response = await getGroupChatRecord({
    //   ...pageInfo, ...currentGroup
    // })
    //
    // const {code, data} = response?.data
    // if (code === '00000') {
    //   const { result } = data || {}
    //   globalLoading.value = false
    //   if (result && result.length > 0) {
    //     // 将新数据追加到现有列表
    //     dialogueList.value = [...dialogueList.value, ...result]
    //     // console.log('加载更多数据成功，追加到列表中', dialogueList.value)
    //   } else {
    //     // 没有更多数据了
    //     setDataState({ hasMore: false })
    //     // console.log('没有更多数据了')
    //   }
    // }
  } catch (error) {
    console.log('加载更多数据失败:', error)
    // 请求失败时回退页码
    pageInfo.currentPage -= 1
  } finally {
    globalLoading.value = false
  }
}

/**
 * 向上加载数据
 */
const loadPreviousData = async () => {
  // 这里可以实现下拉刷新或加载历史消息的逻辑
  try {
    previousPageInfo.currentPage += 1
    await getList(previousPageInfo, 'front')
    // previousPageInfo.endTime =
  } catch (error) {
    console.log('向上加载数据失败:', error)
  }
  // 示例：模拟刷新逻辑
  // try {
  //   // 重置页码，重新加载第一页
  //   pageInfo.currentPage = 1
  //   const response = await getGroupChatRecord({
  //     ...pageInfo, ...currentGroup
  //   })
  //   // 处理响应...
  // } catch (error) {
  //   console.log('刷新失败:', error)
  // }
}

// 使用无限滚动 hooks
const {
  handleScroll,
  setDataState,
  isLoadingMore
} = useInfiniteScroll({
  bottomThreshold: 150,      // 距离底部100px时触发
  topThreshold: 150,          // 距离顶部510px时触发
  throttleDelay: 300,        // 300ms节流
  onLoadMore: loadMoreData,  // 向下加载回调
  onLoadPrevious: loadPreviousData, // 向上加载回调
  debug: false                // 开启调试日志
})

// 注册组件
// const components = {
//   DynamicScroller,
//   DynamicScrollerItem
// }
</script>

<template>
<div class="dialog-container">
  <jb-mobile-select
    ref="selectRef"
    popup-title="请选择群聊"
    :field-names="fieldNames"
    :options="dialogGroupList"
    v-model:model-value="currentGroup.wechatGroupId"
  >
    <template #reference>
      <div class="dialog-container__header">
        {{ selectedGroupName || '请选择群聊' }}
        <img class="pull-down" src="@/assets/images/common/pull-down.webp" alt="" />
      </div>
    </template>
  </jb-mobile-select>
  <div class="dialog-container-content">
    <div class="more-msg" v-if="showMoreMsg" @click.stop="handleShowMorePrevious"><img
        src="@/assets/images/webp/previous-load.webp" alt=""/>更多消息
    </div>
    <dynamic-scroller
        ref="dialogueContentRef"
        class="dialogue-content"
        :items="dialogueList"
        :min-item-size="60"
        key-field="msgId"
        listClass="dialogue-list-scroll-inner"
        @scroll="handleScroll"
        :key="forceUpdateKey"
        v-loading:bg.bgNone="globalLoading"
        v-if="dialogueList && dialogueList.length > 0"
    >
      <template v-slot="{ item, index, active }">
        <dynamic-scroller-item
            :item="item"
            :active="active"
            :size-dependencies="[item.msgTime, item.content, item.msgType]"
            :data-index="index"
        >
          <chat-bubble
            :content="item.content"
            :message-type="item.msgType"
            :message-time="item.msgTime"
            :user-name="(item.fromIsStaff === 1 ? item.staffName : item.customerRemark) || ''"
            :avatar="item.fromIsStaff === 1 ? item.staffAvatar : item.avatar"
            :is-staff="item.fromIsStaff === 1"
            :link-url="item.linkContent?.link_url || item.linkContent?.link?.link_url"
            :link-title="item.linkContent?.link?.title"
            :message-type-name="item.msgName"
          />
        </dynamic-scroller-item>
      </template>
    </dynamic-scroller>
    <jb-mobile-empty v-else :empty-text="'暂无消息'"></jb-mobile-empty>
  </div>
</div>
</template>

<style scoped lang="less">
.dialog-container {
  position: relative;
  height: 100vh;
  background: #F4F4F4;
  box-sizing: border-box;
  &-content {
    //padding: 16px 9px;
    height: calc(100% - 42px);
  }
  &__header {
    height: 42px;
    box-sizing: border-box;
    padding: 10px 16px;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #2B2F33;
    font-weight: 500;
    font-family: PingFangSC, PingFang SC;
    background: #FFFFFF;
    .pull-down {
      width: 16px;
      height: 16px;
      margin-left: 4px;
    }
  }
}
.more-msg {
  position: absolute;
  right: 0;
  top: 52px;
  background: #FFFFFF;
  box-shadow: 0px 0px 6px 0px rgba(0,0,0,0.1);
  border-radius: 100px 0px 0px 100px;
  padding: 10px 16px;
  color: var(--theme-color);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  z-index: 21;
  display: flex;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
  }
}
// 虚拟滚动组件样式
.dialogue-content {
  height: 100%; // 确保虚拟滚动容器有明确高度
  overflow: auto; // 启用滚动
}

.dialogue-list-scroll-inner {
  padding: 16px 9px;
}
</style>

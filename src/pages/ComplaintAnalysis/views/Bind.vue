<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from 'vant'
import { storeToRefs } from 'pinia'
import { cloneDeep } from 'lodash-es'
import * as ww from '@wecom/jssdk'

// stores
import { useUserStore } from '@/stores/user'
import { useApplicationStore } from '@/stores/application'

// components
import { checkHasSafeAreaInsetBottom, JbMobileButton } from 'jb-mobile-ui'
import TreeSelect from '@/components/TreeSelect.vue'
import SelectGroupChatPopup from '@/components/SelectGroupChatPopup.vue'
import JbMobileCascader from '@/components/JbMobileCascader.vue'
import JbMobileConfirmPopup from '@/components/JbMobileConfirmPopup.vue'

// utils
import { getDataFunc, generateRandomId } from '@/utils/common'
import { registerAgentWW } from '@/utils/handleInitWecom.ts'

// api
import { getProjectBuildingInfo } from '@/api/user'
import {
  getUserBindProject,
  getAllGroupInfo,
  getGroupBindBuildingInfo,
  updateGroupBindProject,
} from '@/api/bind'

document.title = '楼栋信息绑定'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const applicationStore = useApplicationStore()
const { userInfo } = storeToRefs(userStore) || {}
const { currentOrgInfo } = storeToRefs(applicationStore) || {}

let currentDeleteIndex = -1 // 当前需要删除的表单的索引
let buildingList: Record<string, any> = [] // 当前项目的楼栋信息

const bindingOrganizationId = ref(0) // 当前管家所绑定项目所在地区的 orgId
const bindingHouseId = ref(0) // 当前管家所绑定项目的 houseId
const formDataList = ref<Record<any, any>[]>([]) // 收集表单列表
const groupList = ref<Record<any, any>[]>([]) // 当前项目所有的群聊信息
const isShowConfirmPopup = ref(false) // 是否显示确认弹窗

const fromWechatGroup = computed(() => route.query.fromWechatGroup || '')
const selectedWechatGroupIdList = computed(() => formDataList.value.filter(item => item.wechatGroupId).map(item => item.wechatGroupId))
const submitButtonDisabled = computed(() => formDataList.value.some(
  (item: Record<string, any>) => item.wechatGroupId === ''
))

// 监听当前组织信息变化
watch(() => currentOrgInfo.value, () => {
  if (fromWechatGroup.value) return
  resetForm()
  getAllGroupInfoList()
  getProjectBuildingInfoList()
})

// 获取从聊天工具栏进入的群聊 id
const getWechatGroupId = () => {
  return new Promise(resolve => {
    const { VITE_APP_BIND_BUILDING_AGENT_ID, VITE_APP_BIND_BUILDING_SECRET } = import.meta.env || {} // 获取应用的 agentId、secret
    registerAgentWW({
      agentId: VITE_APP_BIND_BUILDING_AGENT_ID,
      secret: VITE_APP_BIND_BUILDING_SECRET,
      success: (ww: Record<string, any>) => {
        if (typeof ww.getCurExternalChat === 'function') {
          ww.getCurExternalChat({
            success: (res: Record<string, any>) => {
              console.log('getCurExternalChat success:', res)
              resolve(res)
            },
            fail: (res: Record<string, any>) => {
              console.log('getCurExternalChat fail', res)
              resolve(res)
            },
          })
        } else {
          resolve({})
        }
      }
    })
  })
}

// 获取用户绑定的项目信息
const getUserBindProjectInfo = async() => {
  try {
    if (!fromWechatGroup.value) return

    const data = getDataFunc(await getUserBindProject({
      userid: userInfo.value?.userId
    }))

    bindingOrganizationId.value = data?.organizationId || 0
    bindingHouseId.value = data?.houseId || 0
    formDataList.value.forEach((item: Record<string, any>) => {
      item.organizationId = data?.organizationId || 0
      item.houseId = data?.houseId || 0
    })
  } catch (e) {}
}

// 获取项目所有的群聊信息
const getAllGroupInfoList = async() => {
  const dataList = getDataFunc(await getAllGroupInfo({
    houseId: bindingHouseId.value || currentOrgInfo.value?.houseId
  }))

  groupList.value = dataList.filter((item: Record<string, any>) => item.name) || []
}

// 查询群聊绑定的楼栋信息
const getGroupBindBuildingInfoList = async(formItem: Record<string, any> = {}, wechatGroupId: string) => {
  const isInit = Object.keys(formItem).length === 0
  if (wechatGroupId) {
    const dataList = getDataFunc(await getGroupBindBuildingInfo({
      wechatGroupId
    }))

    formItem.wechatGroupId = wechatGroupId
    formItem.groupName = groupList.value.find((item: Record<string, any>) => item.wechatGroupId === wechatGroupId)?.name || ''
    formItem.boundHouseIdList = dataList.map((item: Record<string, any>) => item.houseId)
    formItem.buildingInfo = dataList.map((item: Record<string, any>) => ({
      buildingId: item.houseId,
      buildName: item.houseName,
    }))
  }
  if (isInit) {
    addFormData(formItem)
  }
}

// 获取项目的楼栋信息
const getProjectBuildingInfoList = async() => {
  const dataList = getDataFunc(await getProjectBuildingInfo({
    houseId: bindingHouseId.value || currentOrgInfo.value?.houseId
  }))

  const data = dataList[0]?.child || []
  handleProjectBuildingInfoData(data)
  buildingList = data
  formDataList.value.forEach((item: Record<string, any>) => {
    item.buildingList = cloneDeep(buildingList)
  })
}

// 处理项目的楼栋信息数据
const handleProjectBuildingInfoData = (dataList: Record<string, any>[], parentPath: string[] = []) => {
  dataList.unshift({
    houseName: '全部',
    houseId: -1,
    child: []
  })
  dataList.forEach((item: Record<string, any>) => {
    item.checked = false
    // 记录路径
    item.path = [...parentPath, item.houseName]
    if (item?.child.length > 0) {
      handleProjectBuildingInfoData(item.child, item.path)
    }
  })
}

// 绑定更多群聊
const addFormData = (formData: Record<string, any> = {}) => {
  formDataList.value.push({
    id: generateRandomId(),
    organizationId: bindingOrganizationId.value || currentOrgInfo.value?.orgId,
    houseId: bindingHouseId.value || currentOrgInfo.value?.houseId,
    wechatGroupId: formData.wechatGroupId || '',
    groupName: formData.groupName || '',
    buildingList: cloneDeep(buildingList),
    boundHouseIdList: formData.boundHouseIdList || [],
    buildingInfo: formData.buildingInfo || [],
  })
}

// 删除表单
const deleteFormData = (index: number) => {
  const formItem = formDataList.value[index]
  if (index && (formItem.wechatGroupId || formItem.buildingInfo.length > 0)) {
    isShowConfirmPopup.value = true
    currentDeleteIndex = index
    return
  }
  isShowConfirmPopup.value = false
  formDataList.value.splice(index || currentDeleteIndex, 1)
  currentDeleteIndex = -1
}

// 选择群聊
const handleSelectGroupChat = (formItem: Record<string, any>, groupChatInfo: Record<string, any>) => {
  getGroupBindBuildingInfoList(formItem, groupChatInfo.wechatGroupId)
}

// 选择楼栋信息
const handleSelectBuildingInfo = (formItem: Record<string, any>, buildingInfoList: Record<string, any>[]) => {
  formItem.buildingInfo = buildingInfoList.map(item => ({
    buildingId: item.houseId,
    buildName: item.houseName,
  }))
}

// 重置
const resetForm = () => {
  formDataList.value = []
  addFormData()
}

// 提交
const handleSubmit = async() => {
  if (submitButtonDisabled.value) {
    return
  }

  const params: Record<string, any> = cloneDeep(formDataList.value)
  delete params.buildingList
  delete params.boundHouseIdList
  const result = await updateGroupBindProject(params)
  const { data, message } = result.data

  if (data) {
    await router.push({
      path: '/result'
    })
    resetForm()
  } else {
    showToast(message || '楼栋信息绑定失败')
  }
}

onMounted(async() => {
  addFormData()
  await getUserBindProjectInfo()
  await getAllGroupInfoList()
  await getProjectBuildingInfoList()
  if (fromWechatGroup.value) { // 从企微的聊天工具栏进入
    const res = await getWechatGroupId()
    const { chatId } = res as Record<string, any>
    getGroupBindBuildingInfoList(formDataList.value[0], chatId)
    formDataList.value[0].organizationId = bindingOrganizationId.value
    formDataList.value[0].houseId = bindingHouseId.value
  }
})
</script>

<template>
  <div class="bind-container flex-column">
    <tree-select
      :permissionHouseId="bindingHouseId"
      :dataPermission="!fromWechatGroup"
      :disabled="!!fromWechatGroup"
      :has-all-option="!fromWechatGroup"
    ></tree-select>
    <div class="bind-container__content flex-1">
      <div class="bind-container__content__inner">
        <div class="bind-container__content__title">收集表单</div>
        <div class="bind-container__content__explain">
          当前功能用于绑定企业微信群和楼栋信息关系。<br>请确认信息无误后，填写完整提交
        </div>
        <div
          v-for="(item, index) in formDataList"
          :key="item.id"
          class="bind-container__content__form"
        >
          <div
            v-if="index > 0"
            class="close-icon-wrap"
            @click="deleteFormData(index)"
          >
            <img
              class="close-icon" src="@/assets/images/svg/close-icon.svg"
              alt=""
            />
          </div>
          <div class="bind-container__content__form-item">
            <div class="bind-container__content__form-label">群聊名称</div>
            <div class="bind-container__content__form-content">
              <select-group-chat-popup
                v-model="item.wechatGroupId"
                :data-list="groupList"
                :selected-list="selectedWechatGroupIdList"
                :z-index="9999"
                @confirm="data => handleSelectGroupChat(item, data)"
              ></select-group-chat-popup>
            </div>
          </div>
          <div class="bind-container__content__form-item">
            <div class="bind-container__content__form-label">楼栋信息</div>
            <div class="bind-container__content__form-content">
              <jb-mobile-cascader
                popup-title="请选择楼栋信息"
                v-model="item.boundHouseIdList"
                :options="item.buildingList"
                :field-names="{ label: 'houseName', value: 'houseId' }"
                :disabled="!item.wechatGroupId"
                :z-index="9999"
                @confirm="dataList => handleSelectBuildingInfo(item, dataList)"
              ></jb-mobile-cascader>
            </div>
          </div>
        </div>
        <div class="bind-container__content__button flex-center" @click="addFormData">
          <img class="common-icon" src="@/assets/images/svg/add-icon.svg" alt="" />
          绑定更多群聊
        </div>
      </div>
    </div>
    <div
      class="bind-container__footer"
      :class="{ 'safe-area-inset-bottom': checkHasSafeAreaInsetBottom() }"
    >
      <jb-mobile-button
        type="primary"
        round
        block
        :disabled="submitButtonDisabled"
        @click="handleSubmit"
      >确认并提交信息</jb-mobile-button>
    </div>
    <jb-mobile-confirm-popup
      v-model:visible="isShowConfirmPopup"
      content="是否要删除该群聊信息吗？"
      z-index="9999"
      @confirm="deleteFormData"
      @cancel="isShowConfirmPopup = false"
    ></jb-mobile-confirm-popup>
  </div>
</template>

<style scoped lang="less">
.bind-container {
  height: 100vh;
  background-color: var(--bg-color);

  &__header {
    height: 42px;
    padding: 0 16px;
    background-color: #ffffff;

    .group-chat-icon {
      width: 24px;
      height: 24px;
    }
  }

  &__group-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--base-font-color);
    line-height: 22px;
    margin: 0 4px 0 6px;
  }

  &__content {
    padding: 12px 10px;
    overflow: auto;

    &__inner {
      padding: 16px 12px;
      background-color: #ffffff;
      border-radius: 8px;
    }

    &__title {
      font-size: 16px;
      font-weight: 600;
      color: var(--base-font-color);
      line-height: 22px;
      margin-bottom: 8px;
    }

    &__explain {
      font-size: 12px;
      color: #808080;
      line-height: 18px;
      margin-bottom: 16px;
    }

    &__form {
      padding: 12px;
      background-color: #f8f8f8;
      border-radius: 8px;
      margin-bottom: 16px;
      position: relative;

      .close-icon-wrap {
        width: 24px;
        height: 24px;
        padding: 5px 4px;
        position: absolute;
        top: 0;
        right: 0;

        .close-icon {
          width: 100%;
          height: 100%;
        }
      }

      &-item {
        & + & {
          margin-top: 20px;
        }
      }

      &-label {
        font-size: 14px;
        color: var(--base-font-color);
        line-height: 20px;
        margin-bottom: 8px;

        &::before {
          content: '*';
          font-weight: 500;
          color: #fb3f1f;
          margin-right: 2px;
        }
      }
    }

    &__button {
      height: 36px;
      font-size: 14px;
      color: var(--theme-color);
      line-height: 20px;
      border: 1px solid var(--theme-color);
      border-radius: 6px;

      .common-icon {
        margin-right: 4px;
      }
    }
  }

  &__footer {
    padding: 12px 16px;
    background-color: #ffffff;

    &.safe-area-inset-bottom {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}
</style>
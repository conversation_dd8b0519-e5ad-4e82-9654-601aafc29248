<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

// stores
import { useEventStore } from '@/stores/event'

// components
import { JbMobileTimePicker, JbMobileButton } from 'jb-mobile-ui'

// hooks
import { useUser } from '@/hooks/useUser'

// utils
import { getDataFunc } from '@/utils/common'

// api
import { getMajorEvent } from '@/api/event'

const router = useRouter()
const eventStore = useEventStore()
const { getUserPermissionProject } = useUser()

let isInit = false
let permissionHouseIdList: number[] = []
const isShowTimePicker = ref(false) // 是否显示日期选择弹出层
const dateValueFormat = ref('') // 所选择日期的格式化显示
const timePickerDefaultValue = ref(dayjs().subtract(1, 'day').toDate()) // 默认选择的日期
const eventList = ref<Record<string, any>>([]) // 重大事件列表
const params = reactive({
  startTime: '',
  endTime: '',
})

// 确认选择某个日期时间段
const handleConfirmSelectTime = (data: Record<string, any>) => {
  params.startTime = data.value[0].split(' ')?.[0]
  params.endTime = data.value[1].split(' ')?.[0]
  dateValueFormat.value = data.dateValueFormat

  if (!isInit) return
  getMajorEventList()
}

// 获取重大事件内容
const getMajorEventList = async() => {
  const dataList = getDataFunc(await getMajorEvent(params)) || []
  
  eventList.value = dataList.filter((item: Record<string, any>) => permissionHouseIdList.includes(item.houseId))
}

// 跳转到热榜页
const toTopicPage = () => {
  router.push({
    name: 'topic',
    query: {
      hideTabBar: 1
    }
  })
}

// 跳转至聊天记录页
const toDialogPage = (item: Record<string, any>) => {
  eventStore.setDialogHouseId(item.houseId)
  eventStore.setDialogGroupList(item.groupInfo)
  router.push('/dialog')
}

onMounted(async() => {
  const { houseIdList } = await getUserPermissionProject([])
  permissionHouseIdList = houseIdList || []
  getMajorEventList()
  isInit = true
})
</script>

<template>
  <div class="event-container flex-column">
    <div class="event-container__header sticky-top flex-between-center">
      <div class="flex-column" @click="toTopicPage">
        <img class="event-container__header__main-title" src="@/assets/images/png/ai-major-event.png" alt="" />
        <div class="flex-align-center">
          <div class="event-container__header__sub-title">昨日业主热议</div>
          <img class="common-icon" src="@/assets/images/svg/hot-icon-small.svg" alt="" />
          <img class="common-icon" src="@/assets/images/svg/right-arrow-icon-gray.svg" alt="" />
        </div>
      </div>
      <div class="flex-align-center" @click="isShowTimePicker = true">
        <div class="event-container__header__date-picker">{{ dateValueFormat }}</div>
        <img class="common-icon" src="@/assets/images/svg/drop-down-hollow.svg" alt="" />
      </div>
    </div>
    <div class="event-container__list flex-1">
      <template v-if="eventList.length">
        <div
          v-for="item in eventList"
          :key="item.houseId"
          class="event-container__list-item"
          @click="toDialogPage(item)"
        >
          <div class="event-container__list-item__header flex-between-center">
            <div class="font-weight-500">{{ item.houseName }}</div>
            <div>{{ item.dayTime }}</div>
          </div>
          <div class="event-container__list-item__content">
            <div class="event-container__list-item__content__title">{{ item.tagSecond }}</div>
            <div class="event-container__list-item__content__detail">{{ item.content || '-' }}</div>
          </div>
        </div>
      </template>
      <div v-else class="event-container__empty flex-column-center">
        <img class="event-container__empty__img" src="@/assets/images/png/event-empty.png" alt="" />
        <div class="event-container__empty__label">昨日无大事，一切皆如意~</div>
        <jb-mobile-button
          class="event-container__empty__button"
          type="primary"
          round
          @click="toTopicPage"
        >去看看业主热议</jb-mobile-button>
      </div>
    </div>
    <jb-mobile-time-picker
      v-model:visible="isShowTimePicker"
      :default-value="timePickerDefaultValue"
      date-range-format="YYYY-MM-DD"
      @confirm="handleConfirmSelectTime"
    ></jb-mobile-time-picker>
  </div>
</template>

<style scoped lang="less">
.event-container {
  -webkit-overflow-scrolling: touch;

  &__header {
    height: 52px;
    padding: 0 16px;
    background-color: #ffffff;
    flex-shrink: 0;

    &__main-title {
      width: 86px;
      margin-bottom: 2px;
    }

    &__sub-title {
      font-size: 12px;
      color: #999999;
      line-height: 17px;
    }

    &__date-picker {
      font-size: 14px;
      font-weight: 500;
      color: var(--base-font-color);
      line-height: 20px;
      margin-right: 4px;
    }
  }

  &__list {
    padding: 12px 10px 0;

    &-item {
      background-color: #ffffff;
      border-radius: 8px;

      & + & {
        margin-top: 12px;
      }

      &__header {
        height: 46px;
        font-size: 14px;
        color: var(--base-font-color);
        line-height: 20px;
        padding: 0 16px;
        background: linear-gradient(270deg, #ffffff 0%, #ede7ff 100%);
        border: 1px solid #ffffff;
        border-radius: 8px 8px 0 0;
      }

      &__content {
        padding: 12px 16px 16px;

        &__title {
          font-size: 16px;
          font-weight: 600;
          color: var(--base-font-color);
          line-height: 22px;
          margin-bottom: 6px;
        }

        &__detail {
          font-size: 12px;
          color: #999999;
          line-height: 17px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
        }
      }
    }
  }

  &__empty {
    margin-top: 81px;

    &__img {
      display: block;
      width: 204px;
      height: 204px;
    }

    &__label {
      font-size: 14px;
      color: #999999;
      line-height: 20px;
      margin: 24px 0 12px;
    }

    &__button {
      height: 38px;
    }
  }
}
</style>
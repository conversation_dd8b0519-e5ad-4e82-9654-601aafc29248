<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { showToast } from 'vant'
import dayjs, { type ManipulateType, type OpUnitType } from 'dayjs'

// images
import rank1 from '@/assets/images/svg/rank-1.svg'
import rank2 from '@/assets/images/svg/rank-2.svg'
import rank3 from '@/assets/images/svg/rank-3.svg'

// types
import type { TOrgData } from '@/types/common'

// stores
import { useUserStore } from '@/stores/user'
import { useApplicationStore } from '@/stores/application'

// components
import { JbMobileFilter, JbMobileDatePicker, JbMobileEmpty } from 'jb-mobile-ui'

// hooks
import { useUser } from '@/hooks/useUser'

// utils
import { getDataFunc, parseOrgList } from '@/utils/common'

// api
import { getIAMOrgTreeForComplaint } from '@/api/user'
import { getCommunityRank } from '@/api/rank'

const router = useRouter()
const userStore = useUserStore()
const applicationStore = useApplicationStore()
const { isAreaRole } = storeToRefs(userStore)
const { getUserPermissionProject } = useUser()

let orgOriginalList: TOrgData[] = [] // 组织架构原始列表
let hasHeadquarters = false
let permissionOrgIdList: number[] = []
let permissionHouseIdList: number[] = []
const rankFilterOrgId = Number(localStorage.getItem('rankFilterOrgId')) // 筛选过的地区公司 orgId
const rankImageMap: Record<number, any> = { // 前三名的排名图标映射
  1: rank1,
  2: rank2,
  3: rank3,
}
const resultTypeMap: Record<string, number> = { // 获取项目分数排名的时间类型列表
  'month': 1,
  'quarter': 2
}

const sortFieldFilterList = computed(() => {
  return [
    { label: '客诉总分', value: 'total_score' },
    { label: '基础服务分', value: 'base_service_score' },
    { label: '客情关系得分', value: 'complaints_score' },
  ].concat(isAreaRole.value ? [] : [
    { label: '诉求响应', value: '诉求响应' },
    { label: '服务态度', value: '服务态度' },
    { label: '保洁卫生', value: '保洁卫生' },
    { label: '绿化养护', value: '绿化养护' },
    { label: '电梯运行', value: '电梯运行' },
    { label: '安全守护', value: '安全管理' },
    { label: '公区维修', value: '公区维修' },
    { label: '出行体验', value: '出入体验' },
    { label: '噪音扰民', value: '噪音扰民' },
    { label: '户内维修', value: '户内维修' },
    { label: '水电气暖', value: '水电气暖' },
    { label: '配套设施', value: '配套设施' },
  ])
})

const datePickerType = ref('month') // 时间选择组件当前所选的时间类型
const dateFormatValue = ref('') // 时间选择组件当前所选时间的格式化值
const rankDateArr = ref<string[]>([]) // 时间选择组件当前所选时间值
const projectRankList = ref<Record<string, any>[]>([]) // 项目分数排名列表
const filterList = reactive([ // 筛选列表
  {
    key: 'houseIds',
    type: 'select',
    list: [],
    defaultValue: -1,
    search: true,
    searchPlaceholder: '搜索地区名称',
    placeholder: '选择地区',
  },
  {
    key: 'sortField',
    type: 'select',
    list: sortFieldFilterList.value,
    defaultValue: 'total_score',
    search: false,
    searchPlaceholder: '',
    placeholder: '选择分数',
  },
  {
    key: 'sort',
    type: 'sort',
    list: [
      { label: '升序', value: 'asc' },
      { label: '降序', value: 'desc' },
    ],
    defaultValue: 'asc',
    search: false,
    searchPlaceholder: '',
    placeholder: '排序',
  }
])
const params = reactive<{
  houseIds: number[]
  sortField: string
  sort: number
  resultType: number
  resultMonth: string
  type: number
}>({ // 请求参数
  houseIds: [], // 项目 id, 不传查的是全国
  sortField: 'total_score', // 排序字段, 分项评分传对应字段名字, 客诉总分 - total_score, 基础服务分 - base_service_score, 客情关系得分 - complaints_score
  sort: 0, // 排名的顺序, 0 - 升序, 1 - 降序
  resultType: resultTypeMap[datePickerType.value], // 查询的时间类型
  resultMonth: '', // 查询的月份
  type: 0,
})

const datePickerDefaultValue = computed(() => dayjs().subtract(1, datePickerType.value as ManipulateType).toDate())

// 获取组织架构列表
const getOrgList = async() => {
  let data = getDataFunc(await getIAMOrgTreeForComplaint())
  data = parseOrgList(data) || []
  orgOriginalList = data
  if (data?.length) {
    const { orgIdList, houseIdList, permissionOrgList } = await getUserPermissionProject(data)
    hasHeadquarters = orgIdList.includes(19759)
    permissionHouseIdList = houseIdList || []
    permissionOrgIdList = permissionOrgList.map((item: Record<string, any>) => item.organizationId)
    data = data.filter((item: Record<string, any>) => permissionOrgIdList.includes(item.organizationId))
    const dataList = data?.filter((item: Record<string, any>) => item.child?.length > 0).map((item: Record<string, any>) => {
      return {
        label: item.organizationName,
        value: item.organizationId,
      }
    }) || []

    dataList.unshift({
      label: '全部',
      value: -1
    })

    filterList[0].list = dataList
  }
  params.houseIds = getProjectHouseIdList(hasHeadquarters ? [19759] : permissionOrgIdList)
  getProjectRankList()
}

// 获取项目分数排名
const getProjectRankList = async() => {
  if (permissionHouseIdList.length === 0) return

  params.resultType = resultTypeMap[datePickerType.value]
  params.resultMonth = dayjs(rankDateArr.value[1]).endOf(datePickerType.value as OpUnitType).format('YYYY-MM')
  projectRankList.value = []

  const dataList = getDataFunc(await getCommunityRank(params))
  projectRankList.value = dataList.filter((item: Record<string, any>) => item.organizationId !== 19759).map((item: Record<string, any>, index: number) => {
    return {
      ...item,
      score: item.rank,
      rank: params.sort === 0 ? index + 1 : dataList.length - index,
    }
  })
}

// 确认选择某个筛选项
const handleFilterConfirm = (filterList: Record<string, any>[]) => {
  filterList.forEach((item: Record<string, any>) => {
    const { value } = item.selectedItem
    if (!value) return
    switch(item.key) {
      case 'houseIds':
        localStorage.setItem('rankFilterOrgId', value)
        params.houseIds = getProjectHouseIdList(value === -1 ? (hasHeadquarters ? [19759] : permissionOrgIdList) : [value])
        break
      case 'sortField':
        params.sortField = value
        break
      case 'sort':
        params.sort = value === 'asc' ? 0 : 1
        break
    }
  })

  getProjectRankList()
}

// 获取多个地区公司下面所有项目的 houseId
const getProjectHouseIdList = (orgIdList: number[]) => {
  const orgDataList = orgOriginalList?.filter((item: Record<string, any>) => orgIdList.includes(item.organizationId))
  return orgIdList.includes(19759) ? [] : orgDataList?.reduce((prev: number[], curr: Record<string, any>) => {
    return prev.concat(curr.child?.map((item: Record<string, any>) => item.houseId) || [])
  }, [])
}

// 确认选择某个时间范围
const handleDatePickerConfirm = (time: string[]) => {
  rankDateArr.value = time

  if (orgOriginalList.length > 0) {
    getProjectRankList()
  }
}

// 根据 houseId 从组织树中获取项目信息
const getProjectInfoByHouseId = (list: TOrgData[], houseId: number): TOrgData | undefined => {
  let result
  for (let i = 0; i < list.length; i++) {
    const item = list[i]
    if (item.houseId === houseId) {
      result = item
    }
    if (item.child?.length > 0) {
      result = getProjectInfoByHouseId(item.child, houseId)
    }
    if (result) return result
  }
}

// 跳转到评分页
const toScorePage = (item: Record<string, any>) => {
  if (!permissionHouseIdList.includes(item.houseId)) {
    showToast('暂无权限查看该项目')
    return
  }

  const orgInfo = getProjectInfoByHouseId(orgOriginalList, item.houseId)
  if (orgInfo) {
    applicationStore.setCurrentOrgInfo(orgInfo)
  }
  router.push({
    name: 'index',
    query: {
      datePickerType: datePickerType.value,
      dateFormatValue: dayjs(rankDateArr.value[0]).format('YYYY-MM-DD'),
    }
  })
}
</script>

<template>
  <div class="rank-container">
    <div class="sticky-top">
      <jb-mobile-filter
        :filter-list="filterList"
        :async-load-filter-list="getOrgList"
        @confirm="handleFilterConfirm"
      ></jb-mobile-filter>
    </div>
    <div class="rank-date-picker">
      <jb-mobile-date-picker
        v-model:date-picker-type="datePickerType"
        v-model:date-format-value="dateFormatValue"
        :date-picker-type-list="['month', 'quarter']"
        :z-index="9999"
        :default-value="datePickerDefaultValue"
        @confirm="handleDatePickerConfirm"
      ></jb-mobile-date-picker>
    </div>
    <div class="rank-container__list">
      <template v-if="projectRankList.length > 0">
        <div
          v-for="item in projectRankList"
          :key="item.houseId"
          class="rank-container__list-item flex-between-center"
          @click="toScorePage(item)"
        >
          <div class="flex-align-center flex-1 overflow-hidden">
            <div class="rank-container__rank-num">
              <img v-if="item.rank < 4" class="rank-container__rank-image" :src="rankImageMap[item.rank]" alt="" />
              <template v-else>{{ item.rank }}</template>
            </div>
            <div class="rank-container__project-name ellipsis">{{ item.houseName }}</div>
          </div>
          <div
            class="rank-container__score"
            :class="{
              purple: item.score > 80,
              yellow: item.score <= 80 && item.score >= 60,
              red: item.score < 60,
            }"
          >{{ item.score }}</div>
        </div>
      </template>
      <jb-mobile-empty v-else class="rank-container__list__empty"></jb-mobile-empty>
    </div>
  </div>
</template>

<style scoped lang="less">
.rank-container {
  -webkit-overflow-scrolling: touch;

  .rank-date-picker {
    padding: 10px 26px 16px;
    background-color: #ffffff;
  }

  &__list {
    padding: 12px 10px 0;

    &__empty {
      margin-top: 200px;
    }
  }

  &__list-item {
    height: 52px;
    font-size: 14px;
    line-height: 20px;
    padding: 0 16px;
    background-color: #ffffff;
    border-radius: 12px;

    & + & {
      margin-top: 12px;
    }
  }

  &__rank-image {
    display: block;
  }

  &__rank-num {
    min-width: 24px;
    font-weight: 600;
    color: #999999;
    text-align: center;
    flex-shrink: 0;
  }

  &__project-name {
    font-weight: 500;
    color: var(--base-font-color);
    margin: 0 16px;
  }

  &__score {
    font-family: 'D-DIN-PRO-Bold';
    font-size: 20px;
    line-height: 24px;
  }
}
</style>
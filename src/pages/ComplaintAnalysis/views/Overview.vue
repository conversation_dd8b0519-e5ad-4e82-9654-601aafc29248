<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import {
  TooltipComponent,
  LegendComponent,
  GridComponent,
  GraphicComponent,
  DataZoomComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

dayjs.extend(quarterOfYear)
echarts.use([
  LineChart,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  GraphicComponent,
  DataZoomComponent,
  CanvasRenderer,
])

// images
import riseIcon from '@/assets/images/svg/rise-icon.svg'
import dropIcon from '@/assets/images/svg/drop-icon.svg'

// stores
import { useApplicationStore } from '@/stores/application'

// utils
import { getDataFunc } from '@/utils/common'
import { eventLineChartOptions } from '@/utils/charts/chartPageOptions'

// components
import { JbMobileDatePicker, JbMobileEmpty } from 'jb-mobile-ui'
import TreeSelect from '@/components/TreeSelect.vue'

// api
import {
  getCommunityBaseInfo,
} from '@/api/score'
import {
  getTotalAccepted,
  getCollectionRate,
  getProfit,
  getSatisfaction,
} from '@/api/overview'

const applicationStore = useApplicationStore()
const { currentOrgInfo } = storeToRefs(applicationStore) || {}

let isInit = false
let lineChartObj: Record<string, any> | null = null
// 项目阶段值的映射
const projectStageMap: Record<number, any> = {
  0: {
    label: '未知',
    color: ''
  },
  1: {
    label: '磨合期',
    color: 'yellow'
  },
  2: {
    label: '稳定期',
    color: 'green'
  },
  3: {
    label: '成熟期',
    color: 'blue'
  },
}

// 项目的基本信息
const projectInfoList = reactive([
  {
    key: 'projectStage',
    label: '项目分期：',
    value: ''
  },
  {
    key: 'precinctPhase',
    label: '项目阶段：',
    value: ''
  },
  {
    key: 'precinctArea',
    label: '在管面积：',
    value: ''
  },
  {
    key: 'firstDeliveryDate',
    label: '首次交楼：',
    value: ''
  },
  {
    key: 'handoverHouseNum',
    label: '交付户数：',
    value: ''
  },
  {
    key: 'residentPopulationCount',
    label: '常住户数：',
    value: ''
  },
  {
    key: 'totalAccepted',
    label: '收楼户数：',
    value: ''
  },
])
// 项目的收缴率
const collectionRateInfoList = reactive<Record<string, any>>([
  {
    key: 'current',
    label: '当期收缴率',
    value: 0,
    different: 0,
  },
  {
    key: 'history',
    label: '历史收缴率',
    value: 0,
    different: 0,
  },
])

const chartRef = ref<HTMLElement | null>(null)
const datePickerType = ref('month') // 时间选择组件当前所选的时间类型
const dateFormatValue = ref('') // 时间选择组件当前所选时间的格式化值
const datePickerDefaultValue = ref(dayjs().subtract(1, 'month').toDate()) // 时间选择组件的默认值
const dateArr = ref<string[]>([]) // 时间选择组件当前所选时间的值
const projectInfoData = ref<Record<string, any>>({}) // 项目基本信息
const projectProfitInfoData = ref<Record<string, any>>({}) // 项目利润的基本信息
const satisfactionLineData = ref<Record<string, any>[]>([]) // 项目满意度的折线图数据

// 当前视图是否为项目层级
const isProjectView = computed(() => currentOrgInfo.value?.houseId)
const selectedDateLastYear = computed(() => dayjs(dateArr.value[0]).subtract(1, 'year').year()) // 时间选择组件当前所选时间值的前一个年份

// 监听当前组织信息变化
watch(() => currentOrgInfo.value, () => {
  initData()
})

// 确认选择某个时间范围
const handleDatePickerConfirm = (time: string[]) => {
  dateArr.value = time

  if (!isInit) return
  initProfitAndCollectionData()
}

// 获取项目相关的参数
const getOrgParams = (isPrecinctIdsList: boolean = true) => {
  if (currentOrgInfo.value?.orgId !== 19759) { // 非总部
    if (currentOrgInfo.value?.houseId) { // 项目
      return {
        orgType: 0,
        projectName: currentOrgInfo.value?.houseName,
        ...(isPrecinctIdsList ? { precinctIds: [currentOrgInfo.value?.houseId] } : { precinctId: currentOrgInfo.value?.houseId })
      }
    }
    return {
      orgType: 1,
      areaName: currentOrgInfo.value?.orgName,
    }
  }

  return {
    orgType: 2
  }
}

// 初始化页面数据
const initData = () => {
  initProjectData()
  initProfitAndCollectionData()
}

// 初始化项目、地区、集团的收缴率和利润数据
const initProfitAndCollectionData = () => {
  if (!currentOrgInfo.value?.orgId) return

  getCollectionRateInfo()
  getProfitInfo()
}

// 初始化项目、地区、集团的基本信息和满意度数据
const initProjectData = () => {
  if (!currentOrgInfo.value?.orgId) return

  getProjectBaseInfo()
  getSatisfactionLineData()
}

// 获取项目基本信息
const getProjectBaseInfo = async() => {
  if (isProjectView.value) {
    const data = getDataFunc(await getCommunityBaseInfo({
      houseId: currentOrgInfo.value?.houseId
    }))
    const dataList = getDataFunc(await getTotalAccepted({
      ...getOrgParams()
    }))

    projectInfoList.forEach(item => {
      switch(item.key) {
        case 'firstDeliveryDate':
          item.value = data[item.key]?.split(' ')[0]
          break
        case 'totalAccepted':
          item.value = dataList[0]?.totalAccepted
          break
        default:
          item.value = data[item.key] ? data[item.key] + (item.key === 'precinctArea' ? 'm²' : '') : ''
          break
      }
    })
    projectInfoData.value = data
  }
}

// 获取项目的收缴率信息
const getCollectionRateInfo = async() => {
  const currentDate = dateArr.value[1]?.split(' ')?.[0]
  const dataList = getDataFunc(await getCollectionRate({
    ...getOrgParams(),
    currentDate,
    historyDate: dayjs(currentDate).subtract(1, 'year').format('YYYY-MM-DD'),
  }))

  const currentData = dataList?.find((item: Record<string, any>) => item.dayType === 0) // 当期数据
  const historyData = dataList?.find((item: Record<string, any>) => item.dayType === 1) // 历史数据
  collectionRateInfoList[0].value = (currentData?.currentCollectionRate * 100).toFixed(2)
  collectionRateInfoList[0].different = ((currentData?.currentCollectionRate - historyData?.currentCollectionRate) * 100).toFixed(2)
  collectionRateInfoList[1].value = (currentData?.historicalCollectionRate * 100).toFixed(2)
  collectionRateInfoList[1].different = ((currentData?.historicalCollectionRate - historyData?.historicalCollectionRate) * 100).toFixed(2)
}

// 获取项目的利润信息
const getProfitInfo = async() => {
  const currentDate = dateArr.value[0]?.split(' ')?.[0]
  const lastYear = dayjs(currentDate).subtract(1, 'year')
  projectProfitInfoData.value = getDataFunc(await getProfit({
    ...getOrgParams(),
    currentDate,
    historyDate: lastYear.format('YYYY-MM-DD'),
    lastDay: lastYear.endOf('year').startOf('month').format('YYYY-MM-DD'),
  }))
}

// 获取项目的满意度折线图
const getSatisfactionLineData = async() => {
  const dataList = getDataFunc(await getSatisfaction({
    ...getOrgParams(false),
  }))

  satisfactionLineData.value = dataList || []
  const startYear = 2023 // 统计开始的年份
  const endYear = dayjs().year() // 统计结束的年份
  const currentQuarter = dayjs().quarter() // 当前季度
  const lineChartData: Record<string, any>[] = []
  for (let year = startYear; year <= endYear; year++) {
    for (let quarter = 1; quarter <= 4; quarter++) {
      if (year === endYear && quarter >= currentQuarter) {
        break
      }
      const data = dataList?.find((item: Record<string, any>) => Number(item.year) === year && Number(item.quarter) === quarter)
      lineChartData.push({
        year,
        quarter,
        score: Number(data?.score) || 0,
      })
    }
  }

  // 绘制图表
  nextTick(() => {
    if (dataList.length === 0) return
    if (lineChartObj) {
      lineChartObj.clear()
    }
    lineChartObj = echarts.init(chartRef.value)
    lineChartObj.setOption(eventLineChartOptions({
      dates: lineChartData.map(item => `${item.year}-Q${item.quarter}`),
      values: [lineChartData.map(item => item.score)],
      lineYAxisLabel: '分数',
      legendList: ['季度'],
      hasArea: false,
      xAxisLabelFormatter: (value: string) => {
        const match = value.match(/Q(\d)/)
        return match ? match?.[1] : value
      },
      tooltipFormatter: (params: Record<string, any>) => {
        const data = params[0]
        return `<div>时间: ${data.name}<br/>分数: ${data.data}</div>`
      }
    }))
  })
}

onMounted(() => {
  isInit = true
})
</script>

<template>
  <div class="overview-container">
    <div class="sticky-top">
      <tree-select :type="2" :data-permission="true" @after-load-org-list="initData"></tree-select>
    </div>
    <div v-if="isProjectView" class="overview-container__info-card">
      <div class="overview-container__info-card__title flex-align-center">
        <div class="overview-container__info-card__line"></div>
        基本信息
      </div>
      <div class="overview-container__base-info">
        <div
          v-for="item in projectInfoList"
          :key="item.key"
          class="overview-container__base-info-item flex-align-center"
        >
          <div class="overview-container__base-info-label">{{ item.label }}</div>
          <div
            v-if="item.key === 'projectStage'"
            class="overview-container__project-stage"
            :class="[projectStageMap[projectInfoData.projectStage]?.color]"
          >{{ projectStageMap[projectInfoData.projectStage]?.label || '-' }}</div>
          <div v-else class="overview-container__base-info-value">{{ item.value || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="overview-container__info-card">
      <jb-mobile-date-picker
        v-model:date-picker-type="datePickerType"
        v-model:date-format-value="dateFormatValue"
        :show-date-picker-type="false"
        :default-value="datePickerDefaultValue"
        :max-date="datePickerDefaultValue"
        @confirm="handleDatePickerConfirm"
      ></jb-mobile-date-picker>
      <div class="overview-container__info-card__inner">
        <div class="overview-container__info-card__title flex-align-center">
          <div class="overview-container__info-card__line"></div>
          收缴率
        </div>
        <div class="overview-container__info-content flex-align-center">
          <div
            v-for="item in collectionRateInfoList"
            :key="item.key"
            class="overview-container__info-item flex-1 flex-column-center"
          >
            <div class="overview-container__info-value flex-column flex-center">
              <template v-if="item.value">
                <div class="overview-container__info-num purple">{{ item.value }}%</div>
                <div class="overview-container__info-compare flex-center">
                  <template v-if="item.different !== 0">
                    <img class="overview-container__info-compare-icon" :src="item.different > 0 ? riseIcon : dropIcon" alt="" />
                    <div
                      :class="{
                        rise: item.different > 0,
                        drop: item.different < 0
                      }"
                    >{{ `${item.different > 0 ? '+' : ''}${item.different}` }}%</div>
                  </template>
                  <div v-else>-</div>
                </div>
              </template>
              <div v-else>-</div>
            </div>
            <div class="overview-container__info-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
      <div class="overview-container__info-card__inner">
        <div class="overview-container__info-card__title flex-align-center">
          <div class="overview-container__info-card__line"></div>
          利润
          <div
            class="overview-container__profit-detail"
          >（{{ selectedDateLastYear }}年利润：{{ projectProfitInfoData?.oldProfit ? `${projectProfitInfoData?.oldProfit?.toLocaleString()}元` : '无数据' }}）</div>
        </div>
        <div class="overview-container__info-content flex-center">
          <div class="overview-container__info-item flex-column-center">
            <div class="overview-container__info-value flex-column flex-center">
              <template v-if="projectProfitInfoData.currentProfit">
                <div class="overview-container__info-num purple">{{ projectProfitInfoData.currentProfit?.toLocaleString() }}</div>
                <div class="overview-container__info-compare flex-center">
                  <template v-if="projectProfitInfoData.difference !== 0">
                    <img
                      class="overview-container__info-compare-icon"
                      :src="projectProfitInfoData.difference > 0 ? riseIcon : dropIcon"
                      alt=""
                    />
                    <div
                      :class="{
                        rise: projectProfitInfoData.difference > 0,
                        drop: projectProfitInfoData.difference < 0
                      }"
                    >{{ `${projectProfitInfoData.difference > 0 ? '+' : ''}${projectProfitInfoData.difference?.toLocaleString()}` }}</div>
                  </template>
                  <div v-else>-</div>
                </div>
              </template>
              <div v-else>-</div>
            </div>
            <div class="overview-container__info-label">{{ dateFormatValue }}(元)</div>
          </div>
        </div>
      </div>
    </div>
    <div class="overview-container__info-card">
      <div class="overview-container__info-card__title flex-align-center">
        <div class="overview-container__info-card__line"></div>
        满意度
      </div>
      <div class="overview-container__line-chart">
        <div v-if="satisfactionLineData.length > 0" ref="chartRef" class="overview-container__line-chart__inner"></div>
        <jb-mobile-empty v-else></jb-mobile-empty>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.overview-container {
  &__info-card {
    padding: 16px;
    margin: 12px 10px 0;
    background-color: #ffffff;
    border-radius: 8px;

    &__inner {
      padding-top: 16px;
    }

    &__title {
      font-size: 16px;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 16px;
    }

    &__line {
      width: 4px;
      height: 16px;
      background-color: var(--theme-color);
      margin-right: 8px;
    }
  }

  &__base-info {
    display: grid;
    grid-template-columns: repeat(2, 2fr);
    row-gap: 12px;

    &-item {
      font-size: 14px;
      line-height: 20px;
    }

    &-label {
      color: #999999;
    }

    &-value {
      color: var(--base-font-color);
    }

    &-project-stage {
      font-size: 12px;
      line-height: 18px;
      padding: 0 6px;
      border-radius: 4px;

      &.yellow {
        color: #ff970f;
        border: 1px solid #ff970f;
      }

      &.green {
        color: #0e9976;
        border: 1px solid #0e9976;
      }

      &.blue {
        color: #006ffe;
        border: 1px solid #006ffe;
      }
    }
  }

  &__project-stage {
    font-size: 12px;
    line-height: 18px;
    padding: 0 6px;
    border-radius: 4px;

    &.yellow {
      color: #ff970f;
      border: 1px solid #ff970f;
    }

    &.green {
      color: #0e9976;
      border: 1px solid #0e9976;
    }

    &.blue {
      color: #006ffe;
      border: 1px solid #006ffe;
    }
  }

  &__info-content {
    height: 100px;
    background-color: #f8f8f8;
    border-radius: 8px;
  }

  &__info-value {
    height: 43px;
  }

  &__info-num {
    font-family: 'D-DIN-PRO-Bold';
    font-size: 23px;
    font-weight: bold;
    line-height: 27px;
  }

  &__info-compare {
    font-size: 10px;
    line-height: 14px;
    margin-top: 2px;

    &-icon {
      width: 12px;
      height: 12px;
    }

    .rise {
      color: #0e9976;
    }

    .drop {
      color: #fb3f1f;
    }
  }

  &__info-label {
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    margin-top: 7px;
  }

  &__profit-detail {
    font-size: 14px;
    color: var(--theme-color);
    line-height: 20px;
  }

  &__line-chart {
    height: 265px;

    &__inner {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
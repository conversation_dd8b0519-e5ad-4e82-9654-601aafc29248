<script setup lang="ts">
import { useRouter } from 'vue-router'

// components
import { JbMobileButton } from 'jb-mobile-ui'

// utils
import { initWWConfig } from '@/utils/handleInitWecom'

document.title = '绑定成功'

const router = useRouter()

// 关闭页面，返回企业微信
const handleBackToWechat = () => {
  initWWConfig((ww: Record<string, any>) => {
    ww.closeWindow({
      success: (res: Record<string, any>) => {
        console.log('closeWindow success', res)
      },
      fail: (res: Record<string, any>) => {
        console.log('closeWindow fail', res)
      }
    })
  })
}
</script>

<template>
  <div class="result-container flex-column-center">
    <div class="result-container__close-button" @click="router.back()">关闭</div>
    <img class="result-container__success-icon" src="@/assets/images/svg/success-icon.svg" alt="" />
    <div class="result-container__text">绑定成功</div>
    <jb-mobile-button
      class="result-container__back-button"
      type="primary"
      round
      @click="handleBackToWechat"
    >返回企业微信</jb-mobile-button>
  </div>
</template>

<style scoped lang="less">
.result-container {
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 0%, #f4f4f4 100%);
  position: relative;

  &__close-button {
    font-size: 16px;
    color: #666666;
    line-height: 22px;
    padding: 16px;
    position: absolute;
    top: 0;
    right: 0;
  }

  &__success-icon {
    width: 60px;
    height: 60px;
    margin-top: 54px;
  }

  &__text {
    font-size: 16px;
    color: var(--base-font-color);
    line-height: 22px;
    margin: 18px 0 80px;
  }

  &__back-button {
    width: 242px;
  }
}
</style>
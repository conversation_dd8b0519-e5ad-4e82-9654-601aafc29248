<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'

// images
import topicRank1 from '@/assets/images/svg/topic-rank-1.svg'
import topicRank2 from '@/assets/images/svg/topic-rank-2.svg'
import topicRank3 from '@/assets/images/svg/topic-rank-3.svg'

// stores
import { useEventStore } from '@/stores/event'

// components
import { JbMobileEmpty } from 'jb-mobile-ui'

// hooks
import { useUser } from '@/hooks/useUser'

// utils
import { getDataFunc } from '@/utils/common'

// api
import { getTopList } from '@/api/event'
import { getUserProjectInfo } from '@/api/user'

const router = useRouter()
const eventStore = useEventStore()
const { getUserPermissionProject } = useUser()

let permissionHouseIdList: number[] = []

const rankImageMap: Record<number, any> = { // 前三名的排名图标映射
  0: topicRank1,
  1: topicRank2,
  2: topicRank3,
}

const topicList = ref<Record<string, any>[]>([]) // 热榜列表

// 获取昨日热榜内容
const getTopicList = async() => {
  const data = getDataFunc(await getTopList({
    dayTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  })) || []

  const dataList = data.filter((item: Record<string, any>) => permissionHouseIdList.includes(+item.houseId))
  dataList.sort((a: Record<string, any>, b: Record<string, any>) => b.totalCount - a.totalCount)
  topicList.value = dataList.slice(0, 10)
}

// 跳转至聊天记录页
const toDialogPage = (item: Record<string, any>) => {
  eventStore.setDialogHouseId(item.houseId)
  eventStore.setDialogGroupList(item.groupInfo)
  router.push('/dialog')
}

onMounted(async() => {
  const { houseIdList } = await getUserPermissionProject([])
  permissionHouseIdList = houseIdList || []
  getTopicList()
})
</script>

<template>
  <div class="topic-container flex-column">
    <div class="topic-container__title">
      <img class="topic-container__title-img" src="@/assets/images/png/topic-title.png" alt="" />
      <img class="topic-container__hot-icon" src="@/assets/images/svg/hot-icon-big.svg" alt="" />
    </div>
    <div class="topic-container__list">
      <template v-if="topicList.length > 0">
        <div
          v-for="(item, index) in topicList"
          :key="item.houseId"
          class="topic-container__list-item"
          @click="toDialogPage(item)"
        >
          <div class="topic-container__list-item__rank flex-center">
            <img
              v-if="index < 3"
              class="topic-container__list-item__rank__img"
              :src="rankImageMap[index]"
              alt=""
            />
            <div class="topic-container__list-item__rank__num" :class="{ white: index < 3 }">{{ index + 1 }}</div>
          </div>
          <div class="overflow-hidden">
            <div class="topic-container__list-item__title ellipsis">{{ item.title }}</div>
            <div class="topic-container__list-item__project">{{ item.houseName }}</div>
          </div>
        </div>
      </template>
      <jb-mobile-empty v-else></jb-mobile-empty>
    </div>
  </div>
</template>

<style scoped lang="less">
.topic-container {
  padding: 12px 10px 0;

  &__title {
    width: 115px;
    padding-left: 6px;
    margin-bottom: 12px;
    position: relative;

    &-img {
      display: block;
      width: 100%;
      position: relative;
      z-index: 10;
    }
  }

  &__hot-icon {
    width: 22px;
    height: 22px;
    position: absolute;
    top: -7px;
    right: -10px;
  }

  &__list {
    padding: 16px 12px;
    background-color: #ffffff;
    border-radius: 12px;

    &-item {
      display: flex;

      & + & {
        margin-top: 16px;
      }

      &__rank {
        width: 24px;
        height: 24px;
        margin-right: 9px;
        position: relative;
        flex-shrink: 0;

        &__img {
          width: 100%;
          height: 100%;
          position: absolute;
          top: 0;
          left: 0;
        }

        &__num {
          font-size: 14px;
          font-weight: 600;
          position: relative;
          z-index: 10;

          &.white {
            color: #ffffff;
          }
        }
      }

      &__title {
        font-size: 14px;
        font-weight: 500;
        color: var(--base-font-color);
        line-height: 20px;
        margin: 2px 0;
      }

      &__project {
        font-size: 12px;
        color: #999999;
        line-height: 17px;
      }
    }
  }
}
</style>
<script setup lang="ts">
import { ref, computed } from 'vue'
import { RouterView, useRoute } from 'vue-router'

// images
import event from '@/assets/images/svg/event.svg'
import eventActiveIcon from '@/assets/images/svg/event-active.svg'
import overview from '@/assets/images/svg/overview.svg'
import overviewActiveIcon from '@/assets/images/svg/overview-active.svg'
import score from '@/assets/images/svg/score.svg'
import scoreActiveIcon from '@/assets/images/svg/score-active.svg'
import chart from '@/assets/images/svg/chart.svg'
import chartActiveIcon from '@/assets/images/svg/chart-active.svg'
import rank from '@/assets/images/svg/rank.svg'
import rankActiveIcon from '@/assets/images/svg/rank-active.svg'

const route = useRoute()

const active = computed(() => {
  return route.name as string
})
const hideTabBar = computed(() => Boolean(route.query?.hideTabBar))

const tabBarList = ref([
  {
    key: 'event',
    path: '/event',
    label: '要事',
    icon: event,
    activeIcon: eventActiveIcon,
  },
  {
    key: 'overview',
    path: '/overview',
    label: '概况',
    icon: overview,
    activeIcon: overviewActiveIcon,
  },
  {
    key: 'index',
    path: '/index',
    label: '评分',
    icon: score,
    activeIcon: scoreActiveIcon,
  },
  {
    key: 'chart',
    path: '/chart',
    label: '图表',
    icon: chart,
    activeIcon: chartActiveIcon,
  },
  {
    key: 'rank',
    path: '/rank',
    label: '排名',
    icon: rank,
    activeIcon: rankActiveIcon,
  },
])
</script>

<template>
  <div class="layout-container">
    <router-view v-slot="{ Component }" class="router-view">
      <keep-alive include="Event,Topic,Index,Chart,Rank">
        <component :is="Component" :key="route.fullPath"/>
      </keep-alive>
    </router-view>
    <van-tabbar v-if="!hideTabBar" v-model="active" active-color="var(--theme-color)" :fixed="false" :safe-area-inset-bottom="true">
      <van-tabbar-item
        v-for="item in tabBarList"
        :key="item.key"
        :to="item.path"
        :name="item.key"
        replace
      >
        <span>{{ item.label }}</span>
        <template #icon="props">
          <img :src="props.active ? item.activeIcon : item.icon" alt="" />
        </template>
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<style scoped lang="less">
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100vh;
  overflow: hidden;
  background-color: var(--bg-color);
  .router-view {
    flex: 1 1 0;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 12px;
    // border-radius: 8px;
    //padding-bottom: calc(66px + constant(safe-area-inset-bottom));
    //padding-bottom: calc(66px + env(safe-area-inset-bottom));
    //box-sizing: border-box;
    &::-webkit-scrollbar {
      display: none;
    }
  }
  .van-tabbar {
    border-radius: 16px 16px 0 0;
    overflow: hidden;
    :deep(.van-tabbar-item__icon) {
      img {
        height: 24px;
      }
    }
    :deep(.van-tabbar-item__text) {
      font-size: 12px;
      line-height: 14px;
    }
  }
}
</style>
<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, nextTick, type ComputedRef } from "vue";
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { showToast, showLoadingToast, closeToast } from 'vant'
import * as echarts from "echarts/core";
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart, Bar<PERSON>hart } from "echarts/charts";
import {
  TooltipComponent,
  LegendComponent,
  GridComponent,
  GraphicComponent,
  DataZoomComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import { eventLineChartOptions, typeBarChartOptions, typePieChartOptions } from "@/utils/charts/chartPageOptions";

// stores
import { useApplicationStore } from '@/stores/application'

// components
import { JbMobileDatePicker, JbMobileSelect, JbMobileEmpty } from 'jb-mobile-ui'
import TreeSelect from '@/components/TreeSelect.vue'
import JbMobileTabs from '@/components/JbMobileTabs.vue'

// utils
import { getDataFunc } from '@/utils/common'

// api
import { aiAnalysisChart, commonFilter } from '@/api/chart'

echarts.use([
  LineChart,
  PieChart,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CanvasRenderer,
  BarChart,
  GraphicComponent,
  DataZoomComponent,
]);

const applicationStore = useApplicationStore()
const { currentOrgInfo, currentHouseIdList } = storeToRefs(applicationStore) || {}

const themeColor = '#7b52fe'
const pieColorList = [themeColor, '#fa4894', '#ffc375', '#3cbdff', '#a7c816', '#487bfa']
const timeTypeMap: Record<string, number> = { // 日期类型配置项列表
  'dateRange': 0,
  'week': 0,
  'month': 0,
  'quarter': 1,
  'year': 1
}
const eventTypeSqlMap: Record<string, string> = { // 事件类型的 sql 映射
  negative: 'SELECT id AS tagFirstId,tag_name AS tagFirst FROM ai_complaints_tag WHERE tag_level = 1 AND tag_type = 0',
  positive: 'SELECT id AS tagFirstId,tag_name AS tagFirst FROM ai_complaints_tag WHERE tag_level = 1 AND tag_type = 1',
  neutral: 'SELECT id AS tagFirstId,tag_name AS tagFirst FROM ai_complaints_tag WHERE tag_level = 1 AND tag_type = 3',
}
const lineChartKeyMap: Record<number, any> = { // 折线图相关的配置项
  0: {
    timeUnit: 'day',
    format: 'YYYY-MM-DD'
  },
  1: {
    timeUnit: 'month',
    format: 'YYYY-MM'
  }
}
const countTypeLabelMap: Record<number, string> = { // 统计类型文案的映射
  1: '事件数',
  2: '总人数'
}

let lineChartObj: Record<string, any> | null = null
let pieChartObj: Record<string, any> | null = null
let barChartObj: Record<string, any> | null = null
let isInit = false
const loading = ref(false)
const datePickerType = ref('month')
const dateFormatValue = ref('')
const datePickerDefaultValue = ref(dayjs().subtract(1, 'month').toDate())
const eventRatioType = ref()
const eventRankType = ref()
const pieTitle = ref('')
const periodTabs = ["日", "周", "月", "季", "年"];
const activePeriod = ref(0);
const currentDate = ref("2025年5月7日");
const showDatePicker = ref(false);
const lineChartData = ref([])
const pieChartData = ref<Record<string, any>[]>([])
const barChartData = ref<Record<string, any>[]>([])
const lineChartTotalCount = ref(0)
const currentSelectedBarData = ref()
const countTypeTabsList = [
  { label: '按人统计', value: 2 },
  { label: '按事统计', value: 1 },
]
const eventTabs = [
  { label: '负面', name: 'negative' },
  { label: '正面', name: 'positive' },
  { label: '中性', name: 'neutral' }
];
const eventName = computed(() => eventTabs.find(item => item.name === params.emotion)?.label)
const currentSelectedBarDataName = computed(() =>
  Object.keys(currentSelectedBarData.value || {}).length ?
    `${barChartData.value[currentSelectedBarData.value?.dataIndex]?.tag_first}-${currentSelectedBarData.value?.name}` :
    `${barChartData.value[0].tag_first}-${barChartData.value[0].tag_second}`
)
const currentSelectedBarDataValue = computed(() => currentSelectedBarData.value?.value || barChartData.value[0].tag_second_num)
const countTypeLabel = computed(() => countTypeLabelMap[params.lat])
const activeEvent = ref(0);

const eventLineChartRef = ref();
const typePieChartRef = ref();
const typeBarChartRef = ref();

// 图表的请求参数
const params = reactive<{
  houseId: number[],
  lat: number,
  emotion: string,
  timeType: number,
  orgType: number,
  groupType: number,
  tagFirstId: number | null,
  startTime: string,
  endTime: string
}>({
  houseId: [],
  emotion: 'negative',
  lat: 2, // 1 - 按事统计, 2 - 按人统计
  timeType: 0, // 0 - 天, 周, 月, 1 - 季度
  orgType: 0, // 0 - 总部, 1 - 地区, 2 - 项目
  groupType: 1, // 1 - 折线图, 2 - 饼图, 3 - 柱状图
  tagFirstId: null, // 事件一级标签 id
  startTime: '',
  endTime: ''
})

// 获取负面事件类型列表
const handleEventTypeListData = (data: []) => {
  const eventTypeList = data.map((item: Record<string, any>) => {
    return {
      label: item.tagFirst,
      value: item.tagFirstId
    }
  })
  eventTypeList.unshift({
    label: '全部',
    value: -1
  })
  return eventTypeList
}
const negativeEventTypeList = ref<Record<string, any>[]>([]) // 负面事件类型列表
const getNegativeEventTypeList = async() => {
  if (negativeEventTypeList.value.length > 0) return

  const data = getDataFunc(await commonFilter({
    key: eventTypeSqlMap.negative
  }))
  negativeEventTypeList.value = handleEventTypeListData(data || [])
}
// 获取正面事件类型列表
const positiveEventTypeList = ref<Record<string, any>[]>([]) // 正面事件类型列表
const getPositiveEventTypeList = async() => {
  if (positiveEventTypeList.value.length > 0) return

  const data = getDataFunc(await commonFilter({
    key: eventTypeSqlMap.positive
  }))
  positiveEventTypeList.value = handleEventTypeListData(data || [])
}
// 获取无情绪事件类型列表
const neutralEventTypeList = ref<Record<string, any>[]>([]) // 无情绪事件类型列表
const getNeutralEventTypeList = async() => {
  if (neutralEventTypeList.value.length > 0) return

  const data = getDataFunc(await commonFilter({
    key: eventTypeSqlMap.neutral
  }))
  neutralEventTypeList.value = handleEventTypeListData(data || [])
}
// 事件类型列表
const eventTypeListMap: ComputedRef = computed(() => {
  return {
    negative: negativeEventTypeList.value,
    positive: positiveEventTypeList.value,
    neutral: neutralEventTypeList.value,
  }
})
const eventTypeList = computed(() => eventTypeListMap.value[params.emotion])

// tabs 吸顶
const datePickerContainerRef = ref()
const isStickyTop = ref(false)
const handleScroll = (event: Record<string, any>) => {
  // isStickyTop.value = event.target.scrollTop >= datePickerContainerRef?.value.clientHeight
  lineChartObj?.dispatchAction({
    type: 'hideTip'
  })
  pieChartObj?.dispatchAction({
    type: 'hideTip'
  })
}

// 设置请求参数的组织架构信息
const setOrgRequestParams = () => {
  if (currentOrgInfo.value?.orgId === 19759) { // 总部
    params.orgType = 0
    params.houseId = []
  } else {
    params.houseId = currentHouseIdList.value
    params.orgType = params.houseId.length > 1 ? 1 : 2
  }
}

// 计算柱状图每一列的宽度百分比
const handleBarChartData = (data: Record<string, any>[]) => {
  const maxValue = data[0]?.user_num || data[0]?.tag_second_num
  data.forEach((item: Record<string, any>) => {
    item.valuePercentage = `${((item.user_num || item.tag_second_num) / maxValue * 100).toFixed(2)}%`
  })
}

// 计算饼图每一项的百分比
const handlePieChartData = (data: Record<string, any>[]) => {
  const total: number = data.reduce((curr: number, prev: Record<string, any>) => {
    return curr + (prev.user_num || prev.tag_second_num)
  }, 0)

  data.forEach((item: Record<string, any>) => {
    item.valuePercentage = `${((item.user_num || item.tag_second_num) / total * 100).toFixed(2)}%`
  })
}

// 获取图表数据
const getAiAnalysisChartData = async(groupType: number) => {
  if (loading.value) return
  // showLoadingToast({
  //   message: '加载中...',
  //   forbidClick: true,
  //   duration: 0,
  // })
  loading.value = true

  setOrgRequestParams()
  params.groupType = groupType
  params.timeType = timeTypeMap[datePickerType.value] || 0
  const { line = [], pie = [], bar = [] } = getDataFunc(await aiAnalysisChart(params))
  closeToast()
  loading.value = false

  if (groupType === 1) { // 查询三个图表的数据
    lineChartData.value = line
    handlePieChartData(pie)
    pieChartData.value = pie
    handleBarChartData(bar)
    barChartData.value = bar
  } else if (groupType === 2) { // 查询饼图的数据
    handlePieChartData(pie)
    pieChartData.value = pie
  } else if (groupType === 3) { // 查询柱状图的数据
    handleBarChartData(bar)
    barChartData.value = bar
  }
  nextTick(() => {
    if (line?.length > 0 && groupType === 1) {
      const LineChartInitData = getLineChartInitData()
      line.forEach((item: Record<string, any>) => {
        const data = LineChartInitData.find(dataItem => dataItem.time === item.time)
        if (data) {
          data.event_num = item.user_num || item.event_num
        }
      })
      // 绘制图表
      if (lineChartObj) {
        lineChartObj.clear()
      }
      lineChartObj = echarts.init(eventLineChartRef.value)
      lineChartObj.setOption(eventLineChartOptions({
        dates: LineChartInitData.map((item: Record<string, any>) => dayjs(item.time).format('MM-DD')),
        values: [LineChartInitData.map(item => item.user_num || item.event_num)],
        lineYAxisLabel: '数量',
        hasArea: true,
        hasDataZoom: true,
      }))
      lineChartTotalCount.value = LineChartInitData.reduce((curr, prev) => {
        return curr + (prev.user_num || prev.event_num)
      }, 0)
    }
    if (pie?.length > 0 && [1, 2].includes(groupType)) {
      const pieData = pie.map((item: Record<string, any>, index: number) => {
        return {
          name: item.tag_first,
          value: item.user_num || item.tag_second_num,
          itemStyle: index === 0 ? { color: themeColor } : (index < 6 ? { color: pieColorList[index] } : {})
        }
      })
      const total = pie.reduce((curr: number, prev: Record<string, any>) => {
        return curr + (prev.user_num || prev.tag_second_num)
      }, 0)
      // 绘制图表
      if (pieChartObj) {
        pieChartObj.clear()
      }
      pieChartObj = echarts.init(typePieChartRef.value)
      pieChartObj.setOption(typePieChartOptions(pieData, pieTitle.value, total))
    }
    // if (bar?.length > 0 && [1, 3].includes(groupType)) {
    //   currentSelectedBarData.value = {}
    //   // 绘制图表
    //   if (barChartObj) {
    //     barChartObj.clear()
    //   }
    //   barChartObj = echarts.init(typeBarChartRef.value)
    //   barChartObj.setOption(typeBarChartOptions({
    //     names: bar.map((item: Record<string, any>) => item.tag_second),
    //     values: bar.map((item: Record<string, any>) => item.user_num || item.tag_second_num)
    //   }))
    //   barChartObj.off('click')
    //   barChartObj.on('click', (data: Record<string, any>) => {
    //     currentSelectedBarData.value = data
    //     barChartObj?.setOption({
    //       series: [
    //         {
    //           itemStyle: {
    //             color: (params: Record<string, any>) => {
    //               return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    //                 { offset: 0, color: params.dataIndex === data.dataIndex ? '#615dee' : '#c2c0ff' },
    //                 { offset: 1, color: "rgba(154,150,248,0.3)" },
    //               ])
    //             },
    //           },
    //         },
    //       ],
    //     })
    //   })
    // }
  })
}

// 根据选择的时间类型获取折线图的初始化数据
const getLineChartInitData = () => {
  const initData: Record<string, any>[] = []
  const startTime = dayjs(params.startTime)
  const endTime = dayjs(params.endTime)
  const lineChartKeyObj = lineChartKeyMap[params.timeType]
  for (let start = startTime; start.isBefore(endTime); start = start.add(1, lineChartKeyObj.timeUnit)) {
    initData.push({
      time: start.format(lineChartKeyObj.format),
      event_num: 0
    })
  }
  return initData
}

// 初始化图表数据
const initChartData = () => {
  if (!currentOrgInfo.value?.orgId) return

  params.tagFirstId = null
  eventRatioType.value = null
  eventRankType.value = null
  pieTitle.value = ''
  getAiAnalysisChartData(1)
}

// 切换统计类型
const handleChangeCountType = (value: number) => {
  params.lat = value
  initChartData()
}

// 确认选择某个时间范围
const handleDatePickerConfirm = (time: string[]) => {
  params.startTime = time[0]
  params.endTime = time[1]

  if (!isInit) return
  getAiAnalysisChartData(1)
}

// 处理切换日期失败
const handleChangeDateFail = () => {
  showToast('数据加载中，请勿频繁切换查询时间')
}

// 处理切换类型占比/类型排名的事件类型
const handleChangeEventType = (groupType: number) => {
  if (groupType === 2) { // 类型占比
    pieTitle.value = eventRatioType.value === -1 ? '' : eventTypeList.value.find((item: Record<string, any>) => item.value === eventRatioType.value)?.label
    params.tagFirstId = eventRatioType.value === -1 ? null : eventRatioType.value
  } else if (groupType === 3) { // 类型排名
    params.tagFirstId = eventRankType.value === -1 ? null : eventRankType.value
  }

  getAiAnalysisChartData(eventRatioType.value === -1 ? 1 : groupType)
}

// 监听当前组织信息变化
watch(() => currentOrgInfo.value, () => {
  getAiAnalysisChartData(1)
})

// 监听事件类型 tab 的切换
watch(() => params.emotion, () => {
  initChartData()
})

// 点击时间选择组件
const handleClickDatePicker = () => {
  lineChartObj?.dispatchAction({
    type: 'hideTip'
  })
}

// mock数据
const eventLineData = {
  dates: ["12-01", "12-02", "12-03", "12-04", "12-05", "12-06", "12-07"],
  values: [55, 50, 70, 90, 80, 85, 88],
};

const pieData = [
  { value: 50, name: "安全问题", itemStyle: { color: "#7B52FE" } },
  { value: 25, name: "经营问题" },
  { value: 25, name: "绿化问题" },
];

const barLineData = {
  names: ["其它问题", "秩序问题", "工程问题", "服务问题", "服务", "环境", "安全", "经营", "绿化"],
  values: [80, 70, 70, 40, 60, 50, 40, 30, 20],
};
const mobileLoading = ref(false);
const isTopOfScroll = ref(true);
onMounted(async() => {
  isInit = true
  await getNegativeEventTypeList()
  await getPositiveEventTypeList()
  await getNeutralEventTypeList()
  // await nextTick(() => {
  //   // 事件分析 - 折线图
  //   if (eventLineChartRef.value) {
  //     const chart = echarts.init(eventLineChartRef.value);
  //     chart.setOption(eventLineChartOptions(eventLineData));
  //   }
  //
  //   // 类型占比 - 饼图
  //   if (typePieChartRef.value) {
  //     const pieChart = echarts.init(typePieChartRef.value);
  //     pieChart.setOption(typePieChartOptions(pieData));
  //   }
  //
  //   // 类型排名 - 柱状图
  //   if (typeBarChartRef.value) {
  //     const barChart = echarts.init(typeBarChartRef.value);
  //     barChart.setOption(typeBarChartOptions(barLineData));
  //   }
  // });
});
const onRefresh = () => {}
</script>

<template>
<!--  <van-pull-refresh-->
<!--    v-model="mobileLoading"-->
<!--    @refresh="onRefresh"-->
<!--    style="height: 100% !important"-->
<!--    :disabled="!isTopOfScroll"-->
<!--  >-->
    <div class="chart-page-container" @scroll="handleScroll">
      <div class="sticky-top">
        <tree-select :type="2" :data-permission="true" @after-load-org-list="initChartData"></tree-select>
      </div>
      <!-- 统计类型 -->
      <jb-mobile-tabs
        v-model="params.lat"
        :tabs-list="countTypeTabsList"
        @change="initChartData"
      ></jb-mobile-tabs>
      <!-- 时间选择器 -->
      <div ref="datePickerContainerRef" class="date-picker-container">
        <jb-mobile-date-picker
          v-model:date-picker-type="datePickerType"
          v-model:date-format-value="dateFormatValue"
          :disabled="loading"
          :z-index="9999"
          :default-value="datePickerDefaultValue"
          @confirm="handleDatePickerConfirm"
          @change-date-fail="handleChangeDateFail"
          @click="handleClickDatePicker"
        ></jb-mobile-date-picker>
      </div>
      <!-- 事件类型切换 -->
      <van-tabs class="event-type-tabs" :class="{ sticky: isStickyTop }" v-model:active="params.emotion"  color="#7B52FE"
           title-inactive-color="#999999" title-active-color="#7B52FE" :ellipsis="false">
        <van-tab
          v-for="tab in eventTabs"
          :key="tab.name"
          :title="tab.label"
          :name="tab.name"
        >
        </van-tab>
      </van-tabs>
      <div class="flex-1 scroll-wrap">
        <!-- 事件分析区块 -->
        <div class="block card line">
          <div class="block-title">
            <span class="block-title-indicator"></span>
            趋势分析
          </div>
          <template v-if="lineChartData.length > 0">
            <div class="event-summary-row">
              <div class="event-summary-label">{{ eventName }}{{ countTypeLabel }}</div>
              <div class="event-summary-value" :class="{ [params.emotion]: params.emotion }">{{ lineChartTotalCount }}</div>
            </div>
            <div class="event-line-chart" ref="eventLineChartRef"></div>
          </template>
          <jb-mobile-empty v-else></jb-mobile-empty>
        </div>
        <!-- 类型占比区块 -->
        <div class="block card pie">
          <div class="block-title-row">
            <div class="block-title">
              <span class="block-title-indicator"></span>
              类型占比
            </div>
            <jb-mobile-select
              v-model="eventRatioType"
              placeholder="选择类型"
              popup-title="请选择事件类型"
              :options="eventTypeList"
              :z-index="9999"
              @change="handleChangeEventType(2)"
            ></jb-mobile-select>
            <!-- <van-dropdown-menu>
            <van-dropdown-item v-model="activeEvent" :options="eventTabs.map((t,i)=>({text:t,value:i}))" />
          </van-dropdown-menu> -->
          </div>
          <div v-if="pieChartData.length > 0" class="type-pie-row">
            <div class="type-pie-chart" ref="typePieChartRef"></div>
            <div class="pie-legend-wrap">
              <div
                v-for="(item, index) in pieChartData.slice(0, 6)"
                :key="item.tag_first_id"
                class="pie-legend-item flex-justify-between"
              >
                <div class="flex-align-center">
                  <div
                    class="pie-legend-dot"
                    :style="{ backgroundColor: pieColorList[index] }"
                  ></div>
                  <div class="pie-legend-label">{{ item.tag_first }}：</div>
                </div>
                <div class="pie-legend-value">{{ item.valuePercentage }}</div>
              </div>
            </div>
          </div>
          <jb-mobile-empty v-else></jb-mobile-empty>
        </div>
        <!-- 类型排名区块 -->
        <div class="block card bar flex-column">
          <div class="block-title-row">
            <div class="block-title">
              <span class="block-title-indicator"></span>
              类型排名
            </div>
            <jb-mobile-select
              v-model="eventRankType"
              placeholder="选择类型"
              popup-title="请选择事件类型"
              :options="eventTypeList"
              :z-index="9999"
              @change="handleChangeEventType(3)"
            ></jb-mobile-select>
            <!-- <van-dropdown-menu>
            <van-dropdown-item v-model="activeEvent" :options="eventTabs.map((t,i)=>({text:t,value:i}))" />
          </van-dropdown-menu> -->
          </div>
          <template v-if="barChartData.length > 0">
            <div v-if="false" class="event-summary-row">
              <div class="event-summary-label">{{ currentSelectedBarDataName }}</div>
              <div class="event-summary-value">{{ currentSelectedBarDataValue }}</div>
            </div>
            <div v-if="false" class="type-bar-chart" ref="typeBarChartRef"></div>
            <div class="bar-chart-wrap flex-1">
              <div
                v-for="item in barChartData"
                :key="item.tag_second_id"
                class="bar-chart-item"
              >
                <div class="bar-chart-title">{{ item.tag_first }}/{{ item.tag_second }}</div>
                <div class="flex-between-center">
                  <div class="flex-1 flex-align-center">
                    <div class="bar-chart-value" :style="{ width: `${item.valuePercentage}` }"></div>
                  </div>
                  <div class="bar-chart-label">{{ item.user_num || item.tag_second_num }}</div>
                </div>
              </div>
            </div>
          </template>
          <jb-mobile-empty v-else></jb-mobile-empty>
        </div>
      </div>
    </div>
<!--  </van-pull-refresh>-->
</template>

<style scoped lang="less">
.chart-page-container {
  background: #f7f8fa;
  //min-height: 100vh;
}
.period-tabs-wrap {
  background: #fff;
  padding: 6px 0 0 0;
}
.custom-tabs {
  :deep(.van-tabs__wrap) {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 16px;
    height: 30px;
  }
  :deep(.van-tabs__nav) {
    background-color: transparent;
    height: 30px;
  }
  :deep(.van-tab) {
    padding: 0;
    flex: 1;
    line-height: 30px;
    font-size: 14px;
    background-color: #f4f4f4;
    transition: all 0.3s;
    color: #323233;
    &--active {
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
      color: #000;
      font-weight: 500;
    }
  }
  :deep(.van-tabs__line) {
    display: none;
  }
}
.date-picker-container {
  padding: 12px 16px 16px 16px;
  background: #ffffff;
}
.date-picker-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 0 16px;
  height: 44px;
  border-radius: 8px;
  margin: 12px 12px 0 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  .date-arrow {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    cursor: pointer;
  }
  .date-text {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 120px;
    justify-content: center;
    cursor: pointer;
    font-weight: 400;
    font-size: 15px;
    color: #2b2f33;
    line-height: 20px;
    .van-icon {
      font-size: 12px;
    }
  }
}
.count-type-tabs {
  font-size: 14px;
  line-height: 20px;
  padding: 16px 0 4px;
  background-color: #ffffff;

  &-inner {
    background-color: var(--bg-color);
    border-radius: 16px;
  }

  &-item {
    padding: 5px 12px;
    border-radius: 16px;

    &.selected {
      color: #ffffff;
      background-color: var(--theme-color);
    }
  }
}
.event-type-tabs {
  display: flex;
  background: #fff;
  border-top: 1px solid #eeeeee;
  border-bottom: 1px solid #eeeeee;
  // margin: 12px 12px 0 12px;
  // border-radius: 8px;
  overflow: hidden;
  width: 100%;
  flex-shrink: 0;
  :deep(.van-tab) {
    font-weight: 500 !important;
    font-size: 14px;
    // height: 53px;
  }
  :deep(.van-tabs__wrap) {
    width: 100%;
    height: 53px;
    :deep(.van-tab__text) {
      font-weight: 500 !important;
      font-size: 14px;
    }
  }
  .event-type-tab {
    flex: 1;
    text-align: center;
    padding: 10px 0;
    font-size: 15px;
    color: #999;
    cursor: pointer;
    background: #fff;
    transition: color 0.2s, background 0.2s;
    &.active {
      color: #7b52fe;
      background: #f4f4f4;
      font-weight: 500;
    }
  }
}
.scroll-wrap {
  overflow-y: auto;
}
.sticky {
  position: sticky;
  left: 0;
  top: 0;
  z-index: 10;
}
.block.card {
  background: #fff;
  border-radius: 8px;
  margin: 12px 12px 0;
  padding: 16px;
  //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;

  &.line {
    height: 403px;
  }

  &.bar {
    height: 396px;
  }
}
.block-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.block-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  .block-title-indicator {
    width: 4px;
    height: 16px;
    background: var(--theme-color);
    margin-right: 8px;
  }
}
.event-summary-row {
  // display: flex;
  // align-items: baseline;
  margin: 12px 0 0 0;
  padding: 8px 12px;
  //background-color: var(--bg-color);
  color: var(--base-font-color);
  background-color: #f5f2ff;
  border-radius: 8px;
  .event-summary-label {
    font-size: 12px;
    color: #4e5969;
    line-height: 17px;
    //margin-right: 8px;
  }
  .event-summary-value {
    font-size: 16px;
    font-weight: bold;
    line-height: 22px;

    //&.negative {
    //  color: #ff0000;
    //}
    //
    //&.positive {
    //  color: #08a57b;
    //}
    //
    //&.neutral {
    //  color: #ff970f;
    //}
  }
}
.event-line-chart {
  width: 100%;
  height: 265px;
  margin-top: 16px;
}
.type-pie-row {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  justify-content: center;
  .type-pie-chart {
    width: 100%;
    height: 203px;
    box-sizing: border-box;
  }
  .type-pie-legend {
    flex: 1;
    margin-left: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    .legend-item {
      display: flex;
      align-items: center;
      font-size: 14px;
      .legend-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
      }
      .legend-label {
        color: #666;
        margin-right: 8px;
      }
      .legend-value {
        color: #7b52fe;
        font-weight: 500;
      }
    }
  }
  .pie-legend-wrap {
    width: 100%;

    .pie-legend-item {
      font-size: 12px;
      color: var(--base-font-color);
      line-height: 18px;
      margin-bottom: 16px;

      &:last-child {
        margin: 0;
      }
    }

    .pie-legend-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 4px;
    }

    .pie-legend-value {
      font-weight: 500;
    }
  }
}
.type-bar-chart {
  width: 100%;
  height: 235px;
  margin-top: 6px;
}

.bar-chart-wrap {
  margin-top: 16px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .bar-chart-item {
    font-size: 12px;
    color: var(--base-font-color);
    line-height: 18px;
    margin-bottom: 20px;

    &:last-child {
      margin: 0;
    }
  }

  .bar-chart-value {
    height: 8px;
    background-color: var(--theme-color);
    border-radius: 4px;
  }

  .bar-chart-label {
    font-weight: 500;
    margin-left: 4px;
  }
}
</style>

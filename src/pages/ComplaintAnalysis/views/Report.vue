<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { showToast, showLoadingToast, closeToast } from 'vant'
import { storeToRefs } from 'pinia'
// import domToImage from 'dom-to-image'
import domToImage from 'dom-to-image-more'
import dayjs from 'dayjs'
import type { OpUnitType, ManipulateType } from 'dayjs'
import {base64ToFile, getToken} from 'jinbi-utils'
import {uploadFile} from "@/api/wecom.ts";
import * as ww from '@wecom/jssdk'

// 导入样式
// 导入 echarts
import * as echarts from 'echarts/core';
import { GaugeChart, LineChart, PieChart, BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import { TitleComponent, TooltipComponent, GraphicComponent, GridComponent, LegendComponent, DataZoomComponent } from 'echarts/components';
import option from '@/utils/charts/scoreChartOptions';
import { createCurrentNationalRank, createLabelContainer } from '@/utils/charts/createEle';
import forwardDisabledSvg from '@/assets/images/svg/forward-disabled.svg';
import forwardSvg from '@/assets/images/svg/forward.svg';
import { JbMobileDatePicker, JbMobileEmpty, JbMobileButton } from 'jb-mobile-ui'
import TreeSelect from '@/components/TreeSelect.vue'
import { eventLineChartOptions, trendLineChartOptions } from '@/utils/charts/chartPageOptions'
echarts.use([GaugeChart, LineChart, PieChart, BarChart, CanvasRenderer, TitleComponent, TooltipComponent, GraphicComponent, GridComponent, LegendComponent, DataZoomComponent]);
const mobileLoading = ref(false) // 下拉刷新
const isTopOfScroll = ref(true) // 是否滚动到顶部
const activeTab = ref(0);
const currentDate = ref('2025年5月');
const score = ref(88);
const ranking = ref('5/8');
const themeColor = '#7b52fe'
const route = useRoute()

// images
import riseIcon from '@/assets/images/svg/rise-icon.svg'
import dropIcon from '@/assets/images/svg/drop-icon.svg'

// stores
import { useUserStore } from '@/stores/user'
import { useApplicationStore } from '@/stores/application'

// utils
import { getDataFunc, copyText } from '@/utils/common'

// api
import {
  getCommunityBaseInfo,
  getRateAndAiResult,
  getAiResult,
  getSysPrompt,
  getAiScoreTrendLineChart,
  getScoreTrend,
  getTagSecondRank,
} from '@/api/score'
import {
  aiAnalysisChart
} from '@/api/chart'

const userStore = useUserStore()
const applicationStore = useApplicationStore()
const { isAreaRole } = storeToRefs(userStore) || {}
const { currentOrgInfo, currentHouseIdList } = storeToRefs(applicationStore) || {}

// 日期类型配置项列表
const timeTypeMap: Record<string, any> = {
  'month': 0,
  'quarter': 1
}
// 项目阶段值的映射
const projectStageMap: Record<number, any> = {
  0: {
    label: '未知',
    color: ''
  },
  1: {
    label: '磨合期',
    color: 'yellow'
  },
  2: {
    label: '稳定期',
    color: 'green'
  },
  3: {
    label: '成熟期',
    color: 'blue'
  }
}
// 获取 ai 评分分析结果的时间类型列表
const resultTypeMap: Record<string, number> = {
  'month': 1,
  'quarter': 2
}
let scoreDateArr: string[] = []
let lineChartObj: Record<string, any> | null = null
let trendLineChartObj1: Record<string, any> | null = null
let trendLineChartObj2: Record<string, any> | null = null
let trendLineChartObj3: Record<string, any> | null = null

// 添加当前时间和最新时间的引用
const latestMonthDate = ref('2025年5月'); // 最新月份
const latestQuarterDate = ref('2025年Q2'); // 最新季度

// 当前选择的组织架构名称
const orgName = computed(() => currentOrgInfo.value?.orgName?.replace('-', ''))

// 判断是否为最新日期的计算属性
const isLatestDate = computed(() => {
  // 根据当前tab判断
  if (activeTab.value === 0) {
    // 月份模式
    return currentDate.value === latestMonthDate.value;
  } else {
    // 季度模式
    return currentDate.value === latestQuarterDate.value;
  }
});

// 前进按钮图标
const forwardIcon = computed(() => {
  return isLatestDate.value ? forwardDisabledSvg : forwardSvg;
});
const datePickerDefaultValue = computed(() => dayjs().subtract(1, datePickerType.value as ManipulateType).toDate())
// 当前视图是否为地区层级
const isAreaView = computed(() => !currentOrgInfo.value?.houseId)
// 当前视图是否为项目层级
const isProjectView = computed(() => currentOrgInfo.value?.houseId)

const scrollRef = ref()
const projectBaseInfoRef = ref()
const loading = ref(false)
const isShowSaveImagePopup = ref(false) // 是否显示保存图片弹出层
const aiResultLoading = ref(false) // ai 评分分析和分项得分是否加载中
const aiScoreItemsLoading = ref(false) // 项目分项得分是否加载中
const aiAnalysisDetail = ref<Record<string, any>>({}) // ai 客诉分析详情
const aiAnalysisResult = ref('') // ai 评分分析
const hasScoreItems = ref(false) // 是否有分项得分的数据
const projectInfoData = ref<Record<string, any>>({}) // 项目基本信息
const isReportView = ref(true) // 是否为报告视图
const trendDataList = ref<Record<string, any>[]>([]) // 趋势分析折线图数据
const issueRankDataList = ref<Record<string, any>[]>([]) // 问题排名数据
// 项目的基本信息
const projectInfoList = reactive([
  {
    key: 'projectStage',
    label: '项目分期：',
    value: ''
  },
  {
    key: 'precinctPhase',
    label: '项目阶段：',
    value: ''
  },
  {
    key: 'firstDeliveryDate',
    label: '首次交楼日期：',
    value: ''
  },
  {
    key: 'precinctArea',
    label: '项目在管面积：',
    value: ''
  },
  {
    key: 'handoverHouseNum',
    label: '已交付户数：',
    value: ''
  },
])
// 项目的基础得分
const projectBaseScoreList = reactive<Record<string, any>[]>([
  {
    label: '基础服务得分',
    value: ''
  },
  {
    label: '客情关系得分',
    value: ''
  },
])
// 项目分项得分
const scoreItems = reactive<Record<string, any>[]>([
  {
    key: '诉求响应',
    label: '诉求响应',
    value: '0',
    lastValue: '0'
  },
  {
    key: '服务态度',
    label: '服务态度',
    value: '0',
    lastValue: '0'
  },
  {
    key: '保洁卫生',
    label: '保洁卫生',
    value: '0',
    lastValue: '0'
  },
  {
    key: '绿化养护',
    label: '绿化养护',
    value: '0',
    lastValue: '0'
  },
  {
    key: '电梯运行',
    label: '电梯运行',
    value: '0',
    lastValue: '0'
  },
  {
    key: '安全管理',
    label: '安全守护',
    value: '0',
    lastValue: '0'
  },
  {
    key: '公区维修',
    label: '公区维修',
    value: '0',
    lastValue: '0'
  },
  {
    key: '出入体验',
    label: '出行体验',
    value: '0',
    lastValue: '0'
  },
  {
    key: '噪音扰民',
    label: '噪音扰民',
    value: '0',
    lastValue: '0'
  },
  {
    key: '户内维修',
    label: '户内维修',
    value: '0',
    lastValue: '0'
  },
  {
    key: '水电气暖',
    label: '水电气暖',
    value: '0',
    lastValue: '0'
  },
  {
    key: '配套设施',
    label: '配套设施',
    value: '0',
    lastValue: '0'
  },
])

let mainIssuesList: string[] = [] // 项目主要问题的列表
let aiScoreFixedPrompt = '' // ai 评分分析的固定提示词
let aiAnalysisResultList: string[] = [] // ai 评分分析返回的结果
let outputTimer = 0 // 流式输出 ai 评分分析结果的定时器
let isStopOutput = false // 是否停止流式输出 ai 评分分析的结果

const trendLineChartConfigList: Record<string, any> = [
  {
    name: '客诉评分',
    color: themeColor,
    domId: 'trend-line-chart-1',
    chartObj: trendLineChartObj1
  },
  {
    name: '基础服务分',
    color: '#fa4894',
    domId: 'trend-line-chart-2',
    chartObj: trendLineChartObj2
  },
  {
    name: '客情关系分',
    color: '#ff970f',
    domId: 'trend-line-chart-3',
    chartObj: trendLineChartObj3
  },
]

const trendData = reactive({
  dates: ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07'],
  scores: [55, 50, 70, 90, 80, 85, 88]
});

const datePickerType = ref('month')
const dateFormatValue = ref('')
const chartRef = ref<HTMLElement | null>(null);
let scoreChart: echarts.ECharts | null = null;

const trendChartRef = ref<HTMLElement | null>(null);
let trendChart: echarts.ECharts | null = null;

// 客诉类型数据
const complaintTypeData = reactive([
  { value: 35, name: '服务态度', itemStyle: { color: '#8678FF' } },
  { value: 25, name: '响应时间', itemStyle: { color: '#FF7A5A' } },
  { value: 20, name: '维修质量', itemStyle: { color: '#FFB74D' } },
  { value: 15, name: '费用争议', itemStyle: { color: '#85CE61' } },
  { value: 5, name: '其他问题', itemStyle: { color: '#5DADE2' } },
]);

const typeChartRef = ref<HTMLElement | null>(null);
let typeChart: echarts.ECharts | null = null;
let controller: Record<string, any> | null = null

// 客诉处理进度数据
const complaintProgress = reactive({
  total: 48,
  handled: 36,
  pending: 8,
  processing: 4
});

// 计算处理率
const handleRate = computed(() => {
  return Math.round((complaintProgress.handled / complaintProgress.total) * 100);
});

// 切换前后月份
const changeDatePrev = () => {
  // 根据当前tab决定如何减少日期
  if (activeTab.value === 0) {
    // 月份模式
    const year = parseInt(currentDate.value.substring(0, 4));
    const month = parseInt(currentDate.value.substring(5, currentDate.value.length - 1));

    // 计算上一个月
    let prevMonth = month - 1;
    let prevYear = year;
    if (prevMonth < 1) {
      prevMonth = 12;
      prevYear -= 1;
    }

    // 更新日期
    currentDate.value = `${prevYear}年${prevMonth}月`;
  } else {
    // 季度模式
    const year = parseInt(currentDate.value.substring(0, 4));
    const quarter = parseInt(currentDate.value.substring(6));

    // 计算上一个季度
    let prevQuarter = quarter - 1;
    let prevYear = year;
    if (prevQuarter < 1) {
      prevQuarter = 4;
      prevYear -= 1;
    }

    // 更新日期
    currentDate.value = `${prevYear}年Q${prevQuarter}`;
  }
};

const changeDateNext = () => {
  // 如果已经是最新日期，则不执行任何操作
  if (isLatestDate.value) {
    return;
  }

  // 根据当前tab决定如何增加日期
  if (activeTab.value === 0) {
    // 月份模式 - 这里是简化示例，实际项目需要更复杂的日期处理
    // 假设格式为"YYYY年M月"
    const year = parseInt(currentDate.value.substring(0, 4));
    const month = parseInt(currentDate.value.substring(5, currentDate.value.length - 1));

    // 计算下一个月
    let nextMonth = month + 1;
    let nextYear = year;
    if (nextMonth > 12) {
      nextMonth = 1;
      nextYear += 1;
    }

    // 更新日期
    currentDate.value = `${nextYear}年${nextMonth}月`;
  } else {
    // 季度模式 - 同样是简化示例
    // 假设格式为"YYYY年QN"
    const year = parseInt(currentDate.value.substring(0, 4));
    const quarter = parseInt(currentDate.value.substring(6));

    // 计算下一个季度
    let nextQuarter = quarter + 1;
    let nextYear = year;
    if (nextQuarter > 4) {
      nextQuarter = 1;
      nextYear += 1;
    }

    // 更新日期
    currentDate.value = `${nextYear}年Q${nextQuarter}`;
  }
};

const initRingContainer = () => {
  setTimeout(() => {
    if (!chartRef.value || !scoreChart) return;

    // 创建标签元素
    const labelContainer = createLabelContainer();

    // 创建全国排名元素
    const rankContainer = createCurrentNationalRank(ranking.value);

    // 确保图表容器是相对定位
    chartRef.value.style.position = 'relative';

    // 添加标签和排名到图表容器
    chartRef.value.appendChild(labelContainer);
    chartRef.value.appendChild(rankContainer);
  }, 100);
}
// 初始化环形图
const initScoreChart = () => {
  if (!chartRef.value) return;

  // 确保最大值为100
  const max = 100;
  const currentValue = score.value;

  // 设置环形图的宽度和阴影宽度
  const mainRingWidth = 25; // 主渐变环宽度
  const outerRingWidth = 10; // 外层
  const shadowRingBg = '#ffffff';

  // 确保字体已加载后再初始化图表
  document.fonts.ready.then(() => {
    scoreChart = echarts.init(chartRef.value);
    initRingContainer();
    scoreChart.setOption(option({max, currentValue, mainRingWidth, outerRingWidth, shadowRingBg}));
  });
};

// 初始化趋势图
const initTrendChart = () => {
  if (!trendChartRef.value) return;

  // 确保字体已加载后再初始化图表
  document.fonts.ready.then(() => {
    trendChart = echarts.init(trendChartRef.value);

    const option = {
      grid: {
        top: 30,
        right: 20,
        bottom: 30,
        left: 50,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderWidth: 0,
        textStyle: {
          color: '#fff',
          fontSize: 12
        },
        formatter: function(params: any) {
          return `
            <div style="padding: 3px 6px;">
              <div>日期: ${params[0].name}</div>
              <div>分数: ${params[0].value}分</div>
            </div>
          `;
        }
      },
      xAxis: {
        type: 'category',
        data: trendData.dates,
        axisLabel: {
          color: '#909399',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        interval: 20,
        axisLabel: {
          color: '#909399',
          fontSize: 12,
          formatter: '{value}'
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#EBEEF5',
            type: 'dashed'
          }
        }
      },
      series: [{
        name: '客诉评分',
        type: 'line',
        data: trendData.scores,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#8678FF'
        },
        lineStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#8678FF' },
              { offset: 1, color: '#7B52FE' }
            ]
          },
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(137, 120, 255, 0.3)' },
              { offset: 1, color: 'rgba(137, 120, 255, 0.05)' }
            ]
          }
        }
      }]
    };

    trendChart.setOption(option);
  });
};

// 初始化客诉类型饼图
const initTypeChart = () => {
  if (!typeChartRef.value) return;

  // 确保字体已加载后再初始化图表
  document.fonts.ready.then(() => {
    typeChart = echarts.init(typeChartRef.value);

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        borderWidth: 0,
        textStyle: {
          color: '#fff',
          fontSize: 12
        }
      },
      legend: {
        orient: 'vertical',
        right: '5%',
        top: 'center',
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        formatter: function(name: string) {
          const item = complaintTypeData.find(item => item.name === name);
          if (item) {
            return `${name}: ${item.value}%`;
          }
          return name;
        },
        textStyle: {
          color: '#606266',
          fontSize: 12
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['30%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: complaintTypeData
        }
      ]
    };

    typeChart.setOption(option);
  });
};

// 测试不同分数的函数
const testDifferentScores = () => {
  if (!scoreChart) return;

  // 模拟不同的分数值
  const testScores = [30, 50, 70, 90, 100];
  let index = 0;

  const interval = setInterval(() => {
    score.value = testScores[index];

    const option = {
      series: [{
        data: [{
          value: score.value
        }]
      }]
    };

    if (scoreChart) {
      scoreChart.setOption(option);
    }

    index = (index + 1) % testScores.length;
  }, 2000); // 每2秒切换一次

  // 60秒后清除定时器
  setTimeout(() => {
    clearInterval(interval);
  }, 60000);
};

// 问题热点区域数据
const hotspotData = reactive([
  { area: 'A区1号楼', count: 15 },
  { area: 'B区2号楼', count: 12 },
  { area: 'C区5号楼', count: 9 },
  { area: 'D区3号楼', count: 7 },
  { area: 'A区4号楼', count: 5 }
]);

const hotspotChartRef = ref<HTMLElement | null>(null);
let hotspotChart: echarts.ECharts | null = null;

// 初始化热点区域图表
const initHotspotChart = () => {
  if (!hotspotChartRef.value) return;

  // 确保字体已加载后再初始化图表
  document.fonts.ready.then(() => {
    hotspotChart = echarts.init(hotspotChartRef.value);

    // 准备数据
    const areas = hotspotData.map(item => item.area);
    const counts = hotspotData.map(item => item.count);

    const option = {
      grid: {
        top: 10,
        right: 10,
        bottom: 40,
        left: 100,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: '{b}: {c}个'
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          color: '#909399',
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#EBEEF5',
            type: 'dashed'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: areas,
        axisTick: {
          show: false
        },
        axisLine: {
          lineStyle: {
            color: '#E4E7ED'
          }
        },
        axisLabel: {
          color: '#606266',
          fontSize: 12
        }
      },
      series: [{
        name: '投诉数量',
        type: 'bar',
        data: counts,
        barWidth: 20,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#8678FF' },
              { offset: 1, color: '#7B52FE' }
            ]
          },
          borderRadius: [0, 4, 4, 0]
        },
        label: {
          show: true,
          position: 'right',
          color: '#606266',
          fontSize: 12
        }
      }]
    };

    hotspotChart.setOption(option);
  });
};

// 滚动相关
const showBackToTop = ref(false);

// 监听滚动事件
const handleScroll = () => {
  const analyticsContainer = document.querySelector('.analytics-container');
  if (analyticsContainer) {
    showBackToTop.value = analyticsContainer.scrollTop > 300;
  }
  lineChartObj?.dispatchAction({
    type: 'hideTip'
  })
};

// 返回顶部
const scrollToTop = () => {
  const analyticsContainer = document.querySelector('.analytics-container');
  if (analyticsContainer) {
    analyticsContainer.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  }
};

// 确认选择某个时间范围
const handleDatePickerConfirm = (time: string[]) => {
  scoreDateArr = time
  isStopOutput = true

  initProjectData()
}

// 处理切换日期失败
const handleChangeDateFail = () => {
  showToast('数据加载中，请勿频繁切换查询时间')
}

// 获取项目相关的数据
const initProjectData = async() => {
  if (loading.value || !currentOrgInfo.value?.orgId) return
  // showLoadingToast({
  //   message: '加载中...',
  //   forbidClick: true,
  //   duration: 0,
  // })
  loading.value = true
  await getProjectBaseInfo()
  await getMainIssuesData()
  await getProjectScoreTrendData()
  await getAiAnalysisResult()
  if (isReportView.value) {
    getIssueRank()
  }
  getAiScoreDetail()
}

// 获取项目基本信息
const getProjectBaseInfo = async() => {
  if (isProjectView.value) {
    const data = getDataFunc(await getCommunityBaseInfo({
      houseId: currentOrgInfo.value?.houseId
    }))

    projectInfoList.forEach(item => {
      item.value = item.key === 'firstDeliveryDate' ? data[item.key]?.split(' ')[0] : data[item.key] + (item.key === 'precinctArea' ? 'm²' : '')
    })
    projectInfoData.value = data
    // nextTick(() => {
    //   const style = window.getComputedStyle(projectBaseInfoRef.value)
    //   scrollRef.value?.scrollTo({
    //     top: projectBaseInfoRef.value?.clientHeight + parseInt(style?.marginTop),
    //   })
    // })
  }
}

// 获取主要问题的数据
const getMainIssuesData = async() => {
  const isRoot = currentOrgInfo.value?.orgId === 19759 // 当前选中的是否为总部
  const houseId = isRoot ? [] : currentHouseIdList.value
  const params = {
    houseId,
    emotion: 'negative',
    timeType: timeTypeMap[datePickerType.value] || 0, // 0 - 月, 1 - 季度
    orgType: isRoot ? 0 : houseId.length > 1 ? 1 : 2, // 0 - 总部, 1 - 地区, 2 - 项目
    groupType: 1, // 1 - 折线图, 2 - 饼图, 3 - 柱状图
    tagFirstId: null, // 事件一级标签 id
    startTime: scoreDateArr[0] || '',
    endTime: scoreDateArr[1] || ''
  }
  const { negative } = getDataFunc(await aiAnalysisChart(params))
  mainIssuesList = negative.map((item: Record<string, any>) => item.tag_second)
}

// 获取项目客诉评分、基础服务分、客情关系分的趋势图
const getProjectScoreTrendData = async() => {
  const year = dayjs(scoreDateArr[1]).startOf('year').format('YYYY')
  const dataList = getDataFunc(await getScoreTrend({
    ...(currentOrgInfo.value?.houseId ? { houseId: currentOrgInfo.value?.houseId } : { organizationId: currentOrgInfo.value?.organizationId || currentOrgInfo.value?.orgId }),
    calculationType: currentOrgInfo.value?.orgId === 19759 ? 2 : currentOrgInfo.value?.houseId ? 0 : 1,
    resultType: resultTypeMap[datePickerType.value],
    year
  }))

  trendDataList.value = dataList || []
  const lineChartData: Record<string, any>[] = []
  for (let i = 0; i < (datePickerType.value === 'month' ? 12 : 4); i++) {
    const time = dayjs(year).startOf(datePickerType.value as ManipulateType).add(i, datePickerType.value as ManipulateType).endOf(datePickerType.value as ManipulateType).format('YYYY-MM')
    const data = dataList.find((item: Record<string, any>) => item.resultMonth === time) || {}
    lineChartData.push({
      time,
      totalScore: Number(data.totalScore) || 0,
      baseServiceScore: Number(data.baseServiceScore) || 0,
      complaintsScore: Number(data.complaintsScore) || 0
    })
  }

  // 绘制图表
  nextTick(() => {
    if (dataList.length === 0) return
    const values = [
      lineChartData.map(item => item.totalScore),
      lineChartData.map(item => item.baseServiceScore),
      lineChartData.map(item => item.complaintsScore),
    ]
    // if (lineChartObj) {
    //   lineChartObj.clear()
    // }
    // lineChartObj = echarts.init(trendChartRef.value)
    // lineChartObj.setOption(eventLineChartOptions({
    //   dates: lineChartData.map(item => dayjs(item.time).format('M')),
    //   values,
    //   lineYAxisLabel: '分数',
    //   hasArea: false,
    //   hasDataZoom: false,
    //   colorList: [themeColor, '#fa4894', '#ff970f'],
    //   legendList: ['客诉评分', '基础服务分', '客情关系分'],
    //   tooltipFormatter: (params: Record<string, any>) => {
    //     const data = params[0]
    //     const scoreStr = params.map((item: Record<string, any>) => `${item.seriesName}：${item.value}`).join('<br/>')
    //     return `<div style='padding:6px 12px;'>月份: ${data.name}月<br/>${scoreStr}</div>`
    //   }
    // }))

    trendLineChartConfigList.forEach((item: Record<string, any>, index: number) => {
      if (item.chartObj) {
        item.chartObj.clear()
      }
      item.chartObj = echarts.init(document.getElementById(`${item.domId}`))
      item.chartObj.setOption(trendLineChartOptions({
        name: item.name,
        xAxisData: lineChartData.map(subItem => dayjs(subItem.time).format('M')),
        seriesData: values[index],
        color: item.color,
      }))
    })
  })
}

// 获取分项得分
const getAiScoreDetail = async() => {
  if (!currentOrgInfo.value?.houseId && currentHouseIdList.value.length === 0) {
    return
  }

  scoreItems.forEach((item: Record<string, any>) => {
    item.value = '0'
  })
  if (controller) {
    controller.abort() // 取消请求
  }
  controller = new AbortController() // 创建控制器，用于取消请求
  aiScoreItemsLoading.value = true
  const responseData: Record<string, any> = await getRateAndAiResult({
    communityIds: currentHouseIdList.value.length ? currentHouseIdList.value : [currentOrgInfo.value?.houseId],
    resultType: resultTypeMap[datePickerType.value],
    startTime: scoreDateArr[0] || '',
    endTime: scoreDateArr[1] || ''
  }, {
    signal: controller?.signal
  })
  if (responseData.code === 'ERR_CANCELED') { // 取消未返回的请求
    return
  }
  aiScoreItemsLoading.value = false

  const { data: { data } } = responseData
  for (const key in data) {
    if (key !== 'aiResult') {
      const index = scoreItems.findIndex(item => key === item.key)
      if (index > -1) {
        scoreItems[index].value = Number(data[key as string])
        scoreItems[index].lastValue = Number(data[`上周期${key}`])
      }
    }
  }
  hasScoreItems.value = scoreItems.some(item => Number(item.value) !== 0)
}

// 获取问题排名
const getIssueRank = async() => {
  const dataList = getDataFunc(await getTagSecondRank({
    houseId: currentHouseIdList.value,
    emotion: 'negative',
    lat: 2,
    startTime: scoreDateArr[0] || '',
    endTime: scoreDateArr[1] || ''
  })) || []

  dataList.sort((a: Record<string, any>, b: Record<string, any>) => b.percentage - a.percentage)
  dataList.forEach((item: Record<string, any>) => {
    item.percent = calculatePercentage(item.percentage)
  })

  issueRankDataList.value = dataList
}

// 计算问题排名的百分比数据
const calculatePercentage = (value: number) => {
  return (100 * (1 - Math.exp(-0.1 * value))).toFixed(2)
}

// 获取 AI 评分分析的固定提示词
const getAiAnalysisFixedPrompt = async() => {
  aiScoreFixedPrompt = getDataFunc(await getSysPrompt({}))
}

// 获取 AI 评分分析的项目信息相关提示词
const getAiAnalysisProjectInfoPrompt = () => {
  return `
这是项目“{${orgName.value}}”的详细信息：

- 项目分期：{${projectStageMap[projectInfoData.value.projectStage]?.label || ''}}
- 所属地区：{${projectInfoData.value.precinctRegion || ''}}
- 项目类型：{${projectInfoData.value.precinctformat || ''}}
- 项目阶段：{${projectInfoData.value.precinctPhase || ''}}
- 已交付户数：{${projectInfoData.value.handoverHouseNum || ''}}
- 常住人口数：{${projectInfoData.value.residentPopulationCount || ''}}

这是项目“{${orgName.value}}”的评分信息：

${scoreItems.map(item => `- ${item.label}：{${item.value}}`).join('\n')}

这是项目“{${orgName.value}}”的主要问题：{${mainIssuesList.join('、')}}。

`
}

// 获取 ai 评分分析
const getAiAnalysisResult = async() => {
  aiResultLoading.value = true
  aiAnalysisResultList = []
  aiAnalysisDetail.value = {}
  aiAnalysisResult.value = ''
  projectBaseScoreList[0].value = ''
  projectBaseScoreList[1].value = ''
  // if (currentOrgInfo.value?.orgId === 19759) {
  //   closeToast()
  //   loading.value = false
  //   aiResultLoading.value = false
  //   return
  // }
  // 如果是当前月份或当前季度则实时请求 ai 的接口获取评分分析
  // if (dayjs().isSame(dayjs(scoreDateArr[1]), datePickerType.value as OpUnitType)) {
  //   fetch('/ai/api/stream/vision/conversion', {
  //     method: 'POST',
  //     headers: {
  //       FrontType: 'egc-admin-ui',
  //       Authorization: getToken('both')
  //     },
  //     body: JSON.stringify({
  //       thinkFlag: 0,
  //       prompt: `${aiScoreFixedPrompt}\n# 需要分析内容：\n\n"""\n${getAiAnalysisProjectInfoPrompt()}"""`
  //     }),
  //   }).then(async response => {
  //     const reader = response.body?.getReader()
  //     const decoder = new TextDecoder()
  //     aiResultLoading.value = false
  //     nextTick(() => {
  //       isStopOutput = false
  //       outputAiAnalysisResult()
  //     })
  //
  //     while (true) {
  //       const {done, value} = await reader?.read() || {}
  //       if (done || isStopOutput) break
  //       const text = decoder.decode(value)
  //       text.split('\n\n').forEach(block => {
  //         if (block.startsWith('data:data:')) {
  //           const result = block.replace('data:data:', '').trim()
  //           if (result) {
  //             aiAnalysisResultList.push(result)
  //           }
  //         }
  //       })
  //     }
  //   })
  // } else {
  const data = getDataFunc(await getAiResult({
    ...(currentOrgInfo.value?.houseId ? { houseId: currentOrgInfo.value?.houseId } : { organizationId: currentOrgInfo.value?.organizationId || currentOrgInfo.value?.orgId }),
    calculationType: currentOrgInfo.value?.orgId === 19759 ? 2 : currentOrgInfo.value?.houseId ? 0 : 1,
    resultType: resultTypeMap[datePickerType.value],
    resultMonth: dayjs(scoreDateArr[1]).endOf(datePickerType.value as OpUnitType).format('YYYY-MM'),
  }))

  closeToast()
  loading.value = false
  aiResultLoading.value = false
  aiAnalysisDetail.value = data || {}
  aiAnalysisResult.value = data?.result
  projectBaseScoreList[0].value = data?.baseServiceScore || 0
  projectBaseScoreList[1].value = data?.complaintsScore || 0
  // }
}

// 流式输出 ai 评分分析的结果
const outputAiAnalysisResult = () => {
  if (isStopOutput) {
    clearTimeout(outputTimer)
    return
  }

  outputTimer = setTimeout(() => {
    const result = aiAnalysisResultList.shift()
    if (result !== undefined) {
      if (result === '[streamEnd]') { // 输出结束
        isStopOutput = true
      } else {
        aiAnalysisResult.value += result
      }
    }

    nextTick(() => {
      outputAiAnalysisResult()
    })
  }, 50)
}

// 保存图片到相册
const handleSaveImage = () => {
  console.log(111)
  domToImage.toPng(document.getElementById('domToImage-container') as HTMLElement).then((base64: string) => {
    console.log(111, base64)
    // ww.chooseImage({
    //   count: 1,
    //   sizeType: ['original', 'compressed'],
    //   sourceType: ['album', 'camera'],
    //   defaultCameraMode: 'batch',
    //   isSaveToAlbum: true
    // })
    uploadImg(base64)
  }, (err) => {
    console.log('111err', err)
  })
}

// 监听当前组织信息变化
watch(() => currentOrgInfo.value, () => {
  projectInfoData.value = {}
  isStopOutput = true
  initProjectData()
})

// 监听窗口大小变化
window.addEventListener('resize', () => {
  if (scoreChart) {
    scoreChart.resize();

    // 移除旧的标签
    if (chartRef.value) {
      const oldLabel = chartRef.value.querySelector('.score-label-container');
      if (oldLabel) {
        chartRef.value.removeChild(oldLabel);
      }
    }
    initRingContainer();
  }

  // 调整趋势图尺寸
  if (trendChart) {
    trendChart.resize();
  }

  // 调整类型图尺寸
  if (typeChart) {
    typeChart.resize();
  }

  // 调整热点区域图尺寸
  if (hotspotChart) {
    hotspotChart.resize();
  }
});
const onRefresh = () => {}
const initData = () => {
  const { startTime, endTime } = route.query
  if (!startTime || !endTime) return
  dateFormatValue.value = route.query.dateFormatValue as string
  scoreDateArr = [dayjs(Number(startTime)).format('YYYY-MM-DD HH:mm:ss'), dayjs(Number(endTime)).format('YYYY-MM-DD HH:mm:ss')]
  initProjectData()
}
watch(() => route.path, () => {
  initData()
})
onMounted(() => {
  initData()
  initScoreChart();
  // initTrendChart();
  // initTypeChart();
  // initHotspotChart();
  getAiAnalysisFixedPrompt()

  // 添加滚动监听
  const analyticsContainer = document.querySelector('.analytics-container');
  if (analyticsContainer) {
    analyticsContainer.addEventListener('scroll', handleScroll);
  }
});
/**
 * @param url 图片的url公网地址
 * */
const previewImg = (url: string) => {
  ww.previewImage({
    current: url,
    urls: [url]
  })
}
const previewFile = (url: string) => {
  ww.previewFile({
    url,
    size: 10 * 1024 * 1024
  })
}
const setClipboardData = (data: string) => {
  ww.setClipboardData({
    data,
    success: (res) => {
      alert(JSON.stringify(res))
      showToast({
        message: '已复制，请在浏览器打开',
        zIndex: 9999,
        teleport: document.body
      })
    },
    fail: (err) => {
      console.log('error', err)
      alert(JSON.stringify(err))
    }
  })
}
/**
 * @param img
 * */
const uploadImg = async (img: string) => {
  const file = base64ToFile(img, Date.now() + '.png')
  // console.log(222, file)
  const data = await uploadFile(file as any)
  console.log(333, data)
  try {
    if (data.data.code === '00000') {
      const {data: url} = data.data || {}
      console.log(444, url)
      // window.open(url, '_blank')
      // await ww.openDefaultBrowser({
      //   url,
      //   success() {
      //     console.log('success')
      //   },
      //   fail() {
      //     console.log('fail')
      //   }
      // })
      const isSuccess = copyText(url)
      showToast({
        message: isSuccess ? '已复制，请在浏览器打开' : '复制失败',
        zIndex: 9999,
        teleport: document.body
      })
      // if (url) {
      //   // previewImg(url)
      //   previewFile(url)
      //   // setClipboardData(url)
      // }
    }
  } catch (err) {
    console.log('err', err)
    alert(JSON.stringify(err))
  }
}
// 组件卸载时移除事件监听
onUnmounted(() => {
  const analyticsContainer = document.querySelector('.analytics-container');
  if (analyticsContainer) {
    analyticsContainer.removeEventListener('scroll', handleScroll);
  }

  // 移除窗口大小变化监听
  window.removeEventListener('resize', () => {
    if (scoreChart) scoreChart.resize();
    if (trendChart) trendChart.resize();
    if (typeChart) typeChart.resize();
    if (hotspotChart) hotspotChart.resize();
  });
});
</script>

<template>
  <div class="report-wrap">
    <div class="project-name">{{ orgName || '-' }}</div>
    <div class="check-date">{{ dateFormatValue }}</div>
    <div class="score-detail flex-column-center">
      <div class="score-circle flex-center">
        <div class="score-circle__inner flex-center">
          <div class="score-circle__content flex-center">
            <div
              class="score-circle__num"
              :class="{
                purple: aiAnalysisDetail.totalScore > 80,
                yellow: aiAnalysisDetail.totalScore <= 80 && aiAnalysisDetail.totalScore >= 60,
                red: aiAnalysisDetail.totalScore < 60,
              }"
            >{{ aiAnalysisDetail.totalScore || '-' }}</div>
          </div>
          <div class="score-circle__title flex-center">客诉评分</div>
        </div>
      </div>
      <div class="score-rank flex-center">
        <div v-if="currentOrgInfo?.orgId !== 19759 && (isAreaView || isProjectView)" class="score-rank__item flex-1">
          当前全国排名 <span class="score-rank__num">{{ aiAnalysisDetail.totalRank || '-' }}/{{ aiAnalysisDetail.totalProject || '-' }}</span>
        </div>
        <div v-if="isProjectView" class="score-rank__item flex-1">
          当前地区排名 <span class="score-rank__num">{{ aiAnalysisDetail.regionRank || '-' }}/{{ aiAnalysisDetail.regionTotalProject || '-' }}</span>
        </div>
      </div>
      <div class="score-base flex-center">
        <div
          v-for="item in projectBaseScoreList"
          :key="item.label"
          class="score-base__item flex-1 flex-column flex-center"
        >
          <div class="score-base__value">
            <div
              v-if="item.value"
              class="score-base__num"
              :class="{
                  purple: item.value > 80,
                  yellow: item.value <= 80 && item.value >= 60,
                  red: item.value < 60,
                }"
            >{{ item.value }}</div>
            <div v-else>-</div>
          </div>
          <div class="score-base__label">{{ item.label }}</div>
        </div>
      </div>
    </div>
    <div class="base-bg-color pt-32 mt-24 br-8 border-top-dashed">
      <div class="section-title mb-16">
        <div class="title-indicator"></div>
        趋势分析
      </div>
      <div v-show="trendDataList.length > 0">
        <div id="trend-line-chart-1" class="line-chart-item"></div>
        <div id="trend-line-chart-2" class="line-chart-item"></div>
        <div id="trend-line-chart-3" class="line-chart-item"></div>
      </div>
      <jb-mobile-empty v-show="trendDataList.length === 0"></jb-mobile-empty>
    </div>
    <div v-if="!isAreaRole" v-loading="aiScoreItemsLoading" class="base-bg-color pt-32 mt-16 br-8 border-top-dashed">
      <div class="section-title flex-justify-between mb-16">
        <div class="left-part flex-align-center">
          <div class="title-indicator"></div>
          分项得分
        </div>
        <div v-if="false" class="score-itemize__basic-service-score">基础服务得分：{{ projectBaseScoreList[0].value }}分</div>
      </div>

      <template v-if="hasScoreItems || true">
        <div class="score-itemize__area">
          <div
            class="score-itemize__item"
            v-for="item in scoreItems.slice(0, 8)"
            :key="item.label"
          >
            <div class="score-itemize__value flex-column flex-center">
              <template v-if="item.value !== '0'">
                <div
                  class="score-itemize__num"
                  :class="{
                    purple: item.value > 80,
                    yellow: item.value <= 80 && item.value >= 60,
                    red: item.value < 60,
                  }"
                >{{ item.value.toFixed(2) }}</div>
                <div class="score-itemize__compare flex-center">
                  <template v-if="item.value !== item.lastValue">
                    <img class="score-itemize__compare-icon" :src="item.value > item.lastValue ? riseIcon : dropIcon" alt="" />
                    <div
                      :class="{
                        rise: item.value > item.lastValue,
                        drop: item.value < item.lastValue
                      }"
                    >{{ `${item.value > item.lastValue ? '+' : ''}${(item.value - item.lastValue).toFixed(2)}` }}</div>
                  </template>
                  <div v-else>-</div>
                </div>
              </template>
              <div v-else>-</div>
            </div>
            <div class="score-itemize__label">{{ item.label }}</div>
          </div>
        </div>
        <div class="score-itemize__area">
          <div
            class="score-itemize__item"
            v-for="item in scoreItems.slice(8)"
            :key="item.label"
          >
            <div class="score-itemize__value flex-column flex-center">
              <template v-if="item.value !== '0'">
                <div
                  class="score-itemize__num"
                  :class="{
                    purple: item.value > 80,
                    yellow: item.value <= 80 && item.value >= 60,
                    red: item.value < 60,
                  }"
                >{{ item.value.toFixed(2) }}</div>
                <div class="score-itemize__compare flex-center">
                  <template v-if="item.value !== item.lastValue">
                    <img class="score-itemize__compare-icon" :src="item.value > item.lastValue ? riseIcon : dropIcon" alt="" />
                    <div
                      :class="{
                    rise: item.value > item.lastValue,
                    drop: item.value < item.lastValue
                  }"
                    >{{ `${item.value > item.lastValue ? '+' : ''}${(item.value - item.lastValue).toFixed(2)}` }}</div>
                  </template>
                  <div v-else>-</div>
                </div>
              </template>
              <div v-else>-</div>
            </div>
            <div class="score-itemize__label">{{ item.label }}</div>
          </div>
        </div>
      </template>
      <jb-mobile-empty v-else></jb-mobile-empty>
    </div>
    <div v-if="isReportView" class="base-bg-color pt-32 mt-32 br-8 border-top-dashed">
      <div class="section-title mb-16">
        <div class="title-indicator"></div>
        问题排名
      </div>
      <template v-if="issueRankDataList.length > 0">
        <div class="bar-chart-wrap flex-1">
          <div
            v-for="item in issueRankDataList"
            :key="item.tagSecondName"
            class="bar-chart-item"
          >
            <div class="bar-chart-title">{{ item.tagSecondName }}</div>
            <div class="flex-between-center">
              <div class="flex-1 flex-align-center">
                <div class="bar-chart-value" :style="{ width: `${item.percent}%` }"></div>
              </div>
              <div class="bar-chart-label">{{ item.percent }}%</div>
            </div>
          </div>
        </div>
      </template>
      <jb-mobile-empty v-else></jb-mobile-empty>
    </div>
  </div>
</template>

<style lang="less" scoped>
.report-wrap {
  height: 100vh;
  padding: 16px;
  padding-bottom: calc(16px + constant(safe-area-inset-bottom));
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  box-sizing: border-box;
  overflow-y: auto;
}
.analytics-container {
  // height: 100%;
  overflow-y: auto;
  //padding: 12px 12px 0;
  -webkit-overflow-scrolling: touch;
}
.customer-complaint-analysis {
  flex-direction: column;
}
.base-bg-color {
  background-color: #ffffff;
}
.section-title {
  display: flex;
  align-items: center;
  // margin: 16px;
  // justify-content: flex-start;

  .left-part {
    display: flex;
  }

  .title-indicator {
    width: 4px;
    height: 16px;
    background-color: var(--theme-color);
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }

  .report-button {
    font-size: 12px;
    color: var(--base-font-color);
    line-height: 18px;
    padding: 3px 10px;
    background-color: var(--bg-color);
    border-radius: 12px;

    &.selected {
      color: var(--theme-color);
      background-color: #f1ecff;
    }
  }
}

.base-info-wrap {
  .base-info-line {
    font-size: 14px;
    line-height: 20px;
  }

  .base-info-label {
    color: #999999;
  }

  .base-info-value {
    color: var(--base-font-color);
  }

  .project-stage {
    font-size: 12px;
    line-height: 18px;
    padding: 0 6px;
    border-radius: 4px;

    &.yellow {
      color: #ff970f;
      border: 1px solid #ff970f;
    }

    &.green {
      color: #0e9976;
      border: 1px solid #0e9976;
    }

    &.blue {
      color: #006ffe;
      border: 1px solid #006ffe;
    }
  }
}

.score-chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF; /* 设置为截图中的浅蓝色背景 */
  position: relative;
  width: 100%;
  padding-bottom: 20px;
  border-bottom: 1px dashed #e4e7ed;

  .score-chart {
    width: 220px;
    height: 220px;
    margin: 20px 0;
    padding: 0;
    border: none;
    box-sizing: border-box;
  }
}

.score-detail {
  //padding: 24px 0 20px;
  padding-top: 24px;

  .score-circle {
    width: 166px;
    height: 166px;
    background-image: conic-gradient(#f7f8fc, #ffffff, #f7f8fc);
    border-radius: 50%;

    &__inner {
      width: 142px;
      height: 142px;
      background-image: conic-gradient(#ffd662 40deg, #a488ff 90deg, var(--theme-color) 110deg, var(--theme-color) 180deg, #ff6a7a 180deg, #ff6a7a 250deg, #ffc522 320deg, #ffd662 360deg);
      border-radius: 50%;
      position: relative;
    }

    &__content {
      width: 94px;
      height: 94px;
      background-color: #ffffff;
      border-radius: 50%;
      position: relative;
    }

    &__title {
      width: 101px;
      height: 32px;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      background-color: var(--theme-color);
      border: 2px solid #ffffff;
      border-radius: 18px;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &__num {
      font-family: 'D-DIN-PRO-Bold';
      font-size: 32px;
      font-weight: 600;
      line-height: 38px;
    }
  }

  .score-rank {
    width: 100%;
    font-size: 14px;
    color: #999999;
    line-height: 20px;
    margin-top: 20px;

    &__item {
      text-align: center;
    }

    &__num {
      color: var(--base-font-color);
    }
  }

  .score-base {
    width: 100%;
    height: 80px;
    margin-top: 16px;
    background-color: #f8f8f8;
    border-radius: 8px;

    &__item {
      position: relative;

      &:last-child {
        &::after {
          display: none;
        }
      }

      &::after {
        content: '';
        width: 1px;
        height: 24px;
        background-color: #e1e1e3;
        border-radius: 1px;
        position: absolute;
        top: 12px;
        right: 0;
      }
    }

    &__value {
      font-family: 'D-DIN-PRO-Bold';
      font-size: 23px;
      font-weight: 600;
      line-height: 27px;
    }

    &__label {
      font-size: 12px;
      color: #999999;
      line-height: 17px;
      margin-top: 4px;
    }
  }
}

.score-analysis {
  //border-top: 1px dashed #e4e7ed;
  //padding-top: 16px;
  //margin: 16px;
  padding-top: 20px;
  border-top: 1px dashed #d2d6dd;

  .analysis-title {
    font-size: 14px;
    font-weight: 500;
    color: #999999;
    line-height: 20px;
    margin-bottom: 8px;
  }

  .analysis-content {
    font-size: 14px;
    color: var(--base-font-color);
    line-height: 26px;
  }
}

.save-image-wrap {
  text-align: right;

  .save-image-inner {
    display: inline-flex;
    width: 104px;
    height: 30px;
    font-size: 14px;
    color: var(--base-font-color);
    background-color: #ffffff;
    border: 1px solid #d2d6dd;
    border-radius: 20px;
  }

  .save-image-icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
}

.score-itemize {
  padding: 16px 8px;

  .section-title {
    margin-left: 8px;
  }

  &__basic-service-score {
    font-size: 12px;
    color: var(--theme-color);
    line-height: 17px;
  }

  &__area {
    padding: 16px 0;
    background-color: #f8f8f8;
    border-radius: 8px;
    display: grid;
    grid-template-columns: repeat(4, 4fr);
    row-gap: 24px;

    & + & {
      margin-top: 8px;
    }
  }

  &__item {
    text-align: center;
  }

  &__value {
    height: 43px;
  }

  &__num {
    font-family: 'D-DIN-PRO-Bold';
    font-size: 23px;
    font-weight: bold;
    line-height: 27px;
  }

  &__compare {
    font-size: 10px;
    line-height: 14px;
    margin-top: 2px;

    &-icon {
      width: 12px;
      height: 12px;
    }

    .rise {
      color: #0e9976;
    }

    .drop {
      color: #fb3f1f;
    }
  }

  &__label {
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    margin-top: 7px;
  }
}

.trend-chart {
  height: 240px;
  position: relative;

  .chart-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    position: relative;
  }
}

.type-chart {
  height: 240px;
  position: relative;

  .chart-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    position: relative;
  }
}

.p-16 {
  padding: 16px;
}

.pt-32 {
  padding-top: 32px;
}

.border-top-dashed {
  border-top: 1px dashed #d2d6dd;
}

.mt-12 {
  margin-top: 12px;
}

.mlr-10 {
  margin-left: 10px;
  margin-right: 10px;
}

.mt-16 {
  margin-top: 16px;
}

.mt-24 {
  margin-top: 32px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-16 {
  margin-bottom: 16px;
}

.br-4 {
  border-radius: 4px;
}

.progress-summary {
  display: flex;
  margin-top: 16px;

  .progress-rate {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding-right: 24px;
    border-right: 1px solid #ebeef5;
    margin-right: 24px;
    width: 100px;

    .rate-circle {
      display: flex;
      align-items: baseline;
      justify-content: center;

      .rate-number {
        font-size: 32px;
        color: #8678FF;
        font-weight: bold;
        line-height: 1;
      }

      .rate-symbol {
        font-size: 16px;
        color: #8678FF;
        margin-left: 2px;
      }
    }

    .rate-label {
      font-size: 14px;
      color: #909399;
      margin-top: 8px;
    }
  }

  .progress-stats {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    flex: 1;

    .stat-item {
      flex: 1;
      min-width: 80px;
      display: flex;
      flex-direction: column;

      .stat-value {
        font-size: 24px;
        font-weight: 500;
        color: #333;

        &.success {
          color: #67C23A;
        }

        &.warning {
          color: #E6A23C;
        }

        &.danger {
          color: #F56C6C;
        }
      }

      .stat-label {
        font-size: 13px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }
}

.hotspot-chart {
  height: 200px;
  position: relative;

  .chart-container {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    position: relative;
  }
}

.issue-rank {
  height: 396px;
  box-sizing: border-box;
}

.back-to-top {
  position: fixed;
  bottom: calc(70px + env(safe-area-inset-bottom));
  right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  &::before {
    content: '';
    width: 12px;
    height: 12px;
    border-top: 2px solid #8678FF;
    border-right: 2px solid #8678FF;
    transform: translateY(3px) rotate(-45deg);
  }
}

//.save-image-popup {
//  padding-top: 42px;
//  background-color: rgba(0, 0, 0, 0.5);
//  position: absolute;
//  top: 0;
//  left: 0;
//  right: 0;
//  bottom: 0;
//  z-index: 9999;
//
//  &__content {
//    width: 91.1vw;
//    height: 80%;
//    font-size: 14px;
//    color: var(--base-font-color);
//    line-height: 20px;
//    background-color: #ffffff;
//    box-sizing: border-box;
//    overflow-y: auto;
//
//    &__inner {
//      padding: 16px;
//      background-color: #ffffff;
//    }
//
//    .section-title {
//      font-size: 16px;
//      font-weight: 500;
//      line-height: 22px;
//    }
//
//    .bar-chart-wrap {
//      margin: 0;
//      overflow: hidden;
//    }
//
    .line-chart-item {
      width: 91.1vw;
      height: 240px;

      & + .line-chart-item {
        margin-top: 24px;
      }
    }
//  }
//
//  &__button {
//    width: 343px;
//    margin-top: 16px;
//  }

  .project-name {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    line-height: 28px;
  }

  .check-date {
    text-align: center;
    margin-top: 8px;
  }
//}

.bar-chart-wrap {
  margin-top: 10px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .bar-chart-item {
    font-size: 12px;
    color: var(--base-font-color);
    line-height: 18px;
    margin-bottom: 20px;

    &:last-child {
      margin: 0;
    }
  }

  .bar-chart-value {
    height: 8px;
    background-color: var(--theme-color);
    border-radius: 4px;
  }

  .bar-chart-label {
    font-weight: 500;
    margin-left: 4px;
  }
}
// 添加响应式布局
@media screen and (min-width: 768px) {
  .analytics-container {
    max-width: 768px;
    margin: 0 auto;
  }

  .score-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .progress-summary {
    .progress-rate {
      width: 120px;
    }

    .progress-stats {
      .stat-item {
        min-width: 120px;
      }
    }
  }
}
</style>

<template>
  <!-- 结果录入/结果输入 -->
  <div class="config">
    <div class="config-item">
      <div class="config-header">
        <img class="config-header-img" src="../../../assets/images/webp/pz.webp" alt="">
        <span class="config-header__text">配置信息</span>
      </div>
      <div class="config-wrap">
        <van-cell
          :title-class="`normal-title ${
            type === 1 ? 'normal-title--required' : ''
          }`"
          :is-link="type === 1"
          title="选择项目"
          :value-class="`normal-value ${
            !itemName ? 'normal-value--unselect' : ''
          }`"
          :value="itemName || '请选择'"
          @click="getProject"
        >
        </van-cell>
        <van-cell
          :title-class="`normal-title ${
            type === 1 ? 'normal-title--required' : ''
          }`"
          :is-link="type === 1"
          title="群聊名称"
          :value-class="`normal-value ${
            !groupPopupData.name ? 'normal-value--unselect' : ''
          }`"
          :value="groupPopupData.name || '请选择'"
          @click="getCrowd"
        >
        </van-cell>
      </div>
    </div>
    <div class="config-generate">
      <div class="config-header">
        <span class="config-header__text" style="margin-left:0px">生成链接</span>
      </div>
      <div class="config-generate__content">
        <div class="url ellipsis" v-if="orderLink">
          {{generateUrl}}
        </div>
        <jb-mobile-button class="url-generate" v-if="!orderLink && itemName && groupPopupData.name" plain round
                          type="primary"
                          @click="generateLink"
          >点击生成链接</jb-mobile-button
        >
        <jb-mobile-button v-if="orderLink" class="url-copy" round type="primary" @click="copyLink"
          >复制链接</jb-mobile-button
        >
      </div>
    </div>
  </div>
    <!-- <select-project-popup
    v-model:orgName="orgName"
    v-model:visible="dateSelectVisible"
    v-model:customizeData="isCustomizeList"
    ref="projectPopup"
    @confirm = "getSure"
  ></select-project-popup> -->
    <!-- 项目选择-从下到上 -->
  <project-select
    v-model:visible="selectPopupVisible" v-model:title="popTitle" v-model:placeholder="popPlaceholder"
    @update:data="getUpData" v-model:customizeData="isCustomizeList" v-model:org-id="selectedOrgId" :has-selected="false"
  ></project-select>
  <project-select
      key-name="wechatGroupId"
      v-model:visible="groupPopupData.visible"
      v-model:title="groupPopupData.title"
      v-model:placeholder="groupPopupData.placeholder"
      @update:data="updateGroupData"
      v-model:customizeData="groupPopupData.list"
      v-model:org-id="groupPopupData.id"></project-select>
</template>

<script setup lang="ts">
import { JbMobileButton } from 'jb-mobile-ui'
import ProjectSelect from '../components/ProjectSelect.vue'
import {reactive, ref} from 'vue'
import {operateOrganizationRelation,queryHouseByUserIdList,queryWechatGroupList} from '@/api/workOrder'
// import { cp } from 'fs'
import { showToast } from 'vant'
import {getConfig, copyText} from "@/utils/common.ts";
// const hostname = import.meta.env.VITE_APP_REDIRECT_URI
// console.log('hostname', hostname);
const groupPopupData = reactive({
  visible: false,
  title: '',
  placeholder: '',
  list: [],
  id: '',
  name: ''
}) // 群聊选择弹窗
const props = defineProps({
  type: {
    type: Number,
    default: 1, // 1.处理结果录入 2.处理结果查看
  },
})
const selectedOrgId = ref('') // 选择的组织架构id
const orderLink = ref(false)
const finishTime = ref('')
const submitRemark = ref('')
const dateSelectVisible = ref(false)
const orgName = ref('请选择组织架构')
const isShowSelectProjectPopup = ref(true)
const selectPopupVisible = ref(false)
// 确认选择上门时间
const confirmSelectTime = (value: string) => {
  finishTime.value = value
}
const projectPopup = ref();
const idx = ref(1)
const popPlaceholder = ref('')
const popTitle = ref('')
const getProject = () => {
  // projectPopup.value.getOrgList()
  popPlaceholder.value = '搜索项目名称';
  popTitle.value = '选择项目';
  idx.value = 1
  getQueryHouseByUserIdList()
}
// 更新群数据
const updateGroupData = (val: any) => {
  if (val && Object.keys(val).length) {
    const { wechatGroupId, name } = val || {}
    Object.assign(groupPopupData, {
      visible: false,
      id: wechatGroupId,
      name
    })
  }
}
const getCrowd = () => {
  Object.assign(groupPopupData, {
    visible: true,
    title: '选择群聊',
    placeholder: '搜索群聊名称',
    // list: [],
    // id: houseId.value
  })
  // idx.value = 2
  // // if(houseId?.value == '' ) return showToast('请先选择项目')
  // popPlaceholder.value = '搜索群聊名称';
  // popTitle.value = '选择群聊';
  getQueryWechatGroupList()
}
const generateLink = () => {
  getOperateOrganizationRelation()
}
// 状态重置
const setValue = (obj: any) => {
  finishTime.value = obj ? obj.finishTime : ''
  submitRemark.value = obj ? obj.submitRemark : ''
}
const copyLink = async () => {
  try {
    await copyText(generateUrl.value)
    // await navigator.clipboard.writeText(generateUrl.value)
    // console.log(generateUrl.value,'generateUrl.value');
    showToast('链接已复制')
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败，请手动复制')
  }
}
defineExpose({
  finishTime,
  submitRemark,
  setValue,
})
const houseId = ref('')
const itemName = ref('')
const wechatGroupId = ref('')
const getUpData = (val: any) => {
  console.log('val', val)
  orderLink.value = false
  if(idx.value === 1){
    houseId.value = val.houseId
    itemName.value = val.houseName
    groupPopupData.id = ''
    groupPopupData.name = ''
  }else{
    wechatGroupId.value = val.wechatGroupId
  }
}
const isCustomizeList = ref([])
const getQueryHouseByUserIdList = async () => {
  try {
    const res = await queryHouseByUserIdList();
    isCustomizeList.value = res.data.data.filter((v:any) => v.houseName != '')
    selectPopupVisible.value = true
    console.log(isCustomizeList.value,'isCustomizeList.value');
  } catch (error) {
    console.error('获取用户数据失败:', error);
  }
}
const generateUrl = ref('') // 生成的链接
const getOperateOrganizationRelation = async () => {
  try {
    // console.log('houseId.value',houseId.value,'wechatGroupId.value',wechatGroupId.value);
    const res = await operateOrganizationRelation({
      houseId:houseId.value,
      wechatGroupId: groupPopupData.id
    });
    if(res.data.code == '0000' || res.data?.data){
      const {VITE_APP_AUTHORIZE_REQUEST_URL, VITE_APP_REDIRECT_URI} = import.meta.env
      const config = await getConfig()
      // generateUrl.value = hostname + `/TaskGenerate/config?houseId=${houseId.value}&wechatGroupId=${wechatGroupId.value}`
      const state = encodeURIComponent(JSON.stringify({
        houseId:houseId.value,
        wechatGroupId: groupPopupData.id,
        isCrowd: 1
      })) + '?wxkey=JBWY'
      generateUrl.value = VITE_APP_AUTHORIZE_REQUEST_URL +
          `?client_id=${config.taskGenerateClientId}&response_type=code&state=${state}&redirect_uri=${VITE_APP_REDIRECT_URI}/TaskGenerate/config`
      console.log('generateUrl.value', generateUrl.value)
      orderLink.value = true
    }
  } catch (error) {
    console.error('获取用户数据失败:', error);
  }
}
interface HouseItem{
  houseId:string | '',
  houseName:string | '',
  name:string | ''
}
const getQueryWechatGroupList = async () => {
  try {
    const res = await queryWechatGroupList({houseId:houseId.value});
    // console.log(res,'queryWechatGroupList');
    groupPopupData.list = res.data.data.filter((item:HouseItem) => item.name).map((item:HouseItem) => ({
    ...item, // 展开原有属性
    houseName: item.name // 添加/覆盖houseName属性
  }));
    // selectPopupVisible.value = true
  } catch (error) {
    console.error('获取用户数据失败:', error);
  }
}

</script>

<style lang="less" scoped>
:deep(.van-cell) {
  padding: 15px 12px;
  overflow: hidden;
  border-radius: 12px;
}
:deep(.van-cell:first-child) {
  border-radius: 0;
  &::after {
    transform: none;
  }
}
:deep(.normal-title) {
  font-weight: 400;
  font-size: 14px;
  color: #2b2f33;
  flex: auto;
}
:deep(.normal-title--required) {
  & > span {
    position: relative;
    &::after {
      content: '*';
      color: #ff3b30;
      position: absolute;
      right: -10px;
      top: 0;
    }
  }
}
:deep(.normal-value) {
  min-width: 220px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  color: #2b2f33;
}
:deep(.normal-value--unselect) {
  color: #999999;
}
.config {
  padding: 12px 10px 0;
  background-color: #f4f4f4;
  height: 100vh;
  box-sizing: border-box;
  &-item {
    background-color: #ffffff;
    border-radius: 12px;
  }
  &-header {
    height: 54px;
    font-weight: 500;
    font-size: 16px;
    color: #2b2f33;
    line-height: 22px;
    text-align: left;
    margin: 0 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f2f4;
    .svg-icon-task-config-icon {
      margin-right: 4px;
    }
    &__text {
      margin-left: 4px;
    }
  }
  &-wrap {
    border-radius: 8px;
    overflow: hidden;
    background-color: #fff;
  }
  &-tip {
    margin-top: 8px;
    font-weight: 400;
    font-size: 10px;
    color: #999999;
    padding-left: 10px;
  }
  &-textarea {
    background: #f4f4f4;
    border-radius: 8px;
    margin-top: 4px;
    padding: 12px !important;
  }
  &-generate {
    height: 108px;
    margin-top: 12px;
    overflow: hidden;
    border-radius: 12px;
    background-color: #fff;
    .config-header {
      height: 48px;
      border-bottom: none;
    }
    &__content {
      padding: 0 12px;
      display: flex;
      .url {
        height: 44px;
        padding: 0 12px;
        line-height: 42px;
        font-weight: 400;
        font-size: 14px;
        color: #2b2f33;
        box-sizing: border-box;
        border: 1px solid #f4f4f4;
        border-radius: 8px;
        margin-right: 10px;
        width: 229px;
        &-generate {
          width: 116px;
          border-radius: 8px;
        }
        &-copy {
          width: 88px;
          border-radius: 8px;
        }
      }
    }
  }
}
.config-generate{
  height: auto !important;
}
.url-generate,.url-copy{
  margin-bottom: 16px;
}
.config-header__text{
  font-size: 14px;
  line-height: 24px;
}
.config-header-img{
  width: 24px;
  height: 24px;
}
</style>

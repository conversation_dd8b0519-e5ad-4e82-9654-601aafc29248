<template>
  <div class="task" ref="targetRef" @scroll="onScroll">
    <div class="task-bg"></div>
    <div
        class="task-header"
        :class="scrollToFixed || selectPopupVisible ? 'fixed' : ''"
    >
      <div class="task-header__left">
        <!-- <SvgIcon name="task-group" size="24" /> -->
        <!-- <SvgIcon name="task-location" size="24" /> -->
        <div v-if="isCrowd" class="company-name ellipsis isCrowd">
          {{ truncatedCompanyName }}
        </div>
        <div
            v-else
            class="company-name ellipsis"
            @click="handleSelectProjectPopupVisible"
        >
          {{ truncatedCompanyName }}
        </div>
        <SvgIcon v-if="!isCrowd" name="pull-down" size="16"/>
      </div>
      <!-- <div class="task-header__right">
        <span>筛选</span>
        <SvgIcon name="task-filter" size="13" />
      </div> -->
      <div
          class="task-header__right"
          :class="selectPopupVisible ? 'active' : ''"
          @click="getSelectPopup"
          v-if="!isCrowd"
      >
        <span>{{ truncatedGroupName }}</span>
        <SvgIcon name="pull-down" size="16"/>
      </div>
    </div>
    <div class="task-middle" ref="middleRef">
      <div class="task-situation">
        <div class="task-situation__filter">
          <span @click.stop="isShowTimePicker = true;">{{ currentTime }}</span>
          <SvgIcon name="task-down" size="16"/>
        </div>
        <div class="task-situation__content">
          <i class="task-demarcation"></i>
          <div class="card">
            <span class="card-label">
              <img v-if="cardImg" src="@/assets/images/common/task-curr.png" alt=""/>
              <span>工单数量</span>
            </span>
            <span class="card-count">{{ workOrderData.totalNum }}</span>
          </div>
          <div class="card">
            <span class="card-label">
              <img v-if="cardImg" src="@/assets/images/common/task-curr.png" alt=""/>
              <span>工单完成率</span>
            </span>
            <span class="card-count">{{ workOrderData.finishRate }}</span>
          </div>
        </div>
      </div>
      <div class="task-finished">
        <div class="task-finished__label">已完成工单数</div>
        <div class="task-finished__item">
          <div class="item" v-for="(item,index) in recentDays" :key="index">
            <div class="item-top" :class="item.cls">
              <span class="count">{{ item.total }}</span>
              <SvgIcon v-if="item.cls === 'highlight'" name="task-score-normal" size="28"/>
              <!-- <SvgIcon v-else name="task-points" size="28" /> -->
              <img v-else :src="textImg" alt="" style="width:30px">

            </div>
            <div class="item-bottom">{{ item.title }}</div>
          </div>
        </div>
      </div>
    </div>
    <div
        class="task-search"
        :class="scrollToFixed ? 'fixed' : ''"
        :style="{ paddingTop: scrollToFixed ? '10px' : '', background: scrollToFixed ? '#fff' : '' }"
    >
      <jb-mobile-search-input placeholder="搜索内容" v-model:modelValue="modelValue" @focus="handleInputFocus"
      ></jb-mobile-search-input>
      <van-tabs
          shrink
          class="tab"
          @change="changeTab"
          v-model:active="taskStatus"
          v-if="categoryStatistics?.length"
      >
        <van-tab
            v-for="(item, index) in categoryStatistics"
            :title="`${item.categoryName}(${item.workOrderNum})`"
            :key="item.categoryId"
            :name="item.categoryName"
            @click="classification"
        >
        </van-tab>
      </van-tabs>
    </div>
    <div class="task-list" ref="taskListRef" :style="{height: computedTaskListHeight + 'px',
     paddingTop: taskListPaddingTop + 'px'}">
      <div class="task-list__item" v-for="(item , index) in orderList" :key="index">
        <div class="job">
          <img v-if="item.categoryId === 3" src="@/assets/images/common/task-type-zx.png" class="job-img"/>
          <img v-if="item.categoryId === 4" src="@/assets/images/common/task-type-lh.png" class="job-img"/>
          <img v-if="item.categoryId === 1" src="@/assets/images/common/task-type-gc.png" class="job-img"/>
          <img v-if="item.categoryId === 5" src="@/assets/images/webp/gc.webp" class="job-img"/>
          <img v-if="item.categoryId === 2" src="@/assets/images/common/task-type-bj.png" class="job-img"/>
          <div class="job-content" @click="getDetail(item)">
            <div class="job-content__title">{{ item.content }}</div>
            <div class="job-content__time">{{ item.workOrderTime }}</div>
          </div>
          <jb-mobile-button
              v-if="!fromWorkSpace && (item.status !== 3 && isCrowd)"
              round
              color="linear-gradient(90deg, #FF8200 1%, #FF4738 100%)"
              class="job-operate job-operate11"
              size="small"
              @click="getWorkOrder(item)"
          >
            <div style="white-space: nowrap;">搞定了</div>
          </jb-mobile-button
          >
          <jb-mobile-button
              v-else-if="item.status !== 3 && !isCrowd"
              round
              color="linear-gradient(90deg, #FF8200 1%, #FF4738 100%)"
              class="job-operate job-operate12"
              size="small"
          >
            <div style="white-space: nowrap;">待完成</div>
          </jb-mobile-button
          >
          <jb-mobile-button
              v-else
              round
              color="linear-gradient(90deg, #FF8200 1%, #FF4738 100%)"
              class="job-operate"
              size="small"
          >
            <div style="white-space: nowrap;">已完成</div>
          </jb-mobile-button>
        </div>
        <div class="task-footer" v-if="orderList && orderList.length > 0 && index === orderList.length - 1">
          <span>当前分类为</span>
          <SvgIcon name="task-ai" size="16"/>
          <span>玄览AI识别，仅供参考</span>
        </div>
      </div>
      <div v-if="orderList && orderList.length === 0" class="empty-state">
        暂无工单数据
      </div>
    </div>
  </div>
  <!-- SelectProjectPopup用于获取组织架构数据 -->
  <select-project-popup
      v-if="false"
      v-model:visible="isShowSelectProjectPopup"
      v-model:orgName="orgName"
      v-model:data="orgData"
      @update:data="handleSelectProjectData"
  ></select-project-popup>

  <!-- 总部组织架构选择弹窗 -->
  <jb-mobile-tree-select-popup
      :padding-top="48"
      v-model:visible="isShowHeadquartersPopup"
      v-model:search-value="orgSearchValue"
      :data-list="orgList"
      :field-names="{ label: 'orgName', value: 'orgId' }"
      :default-selected-value="currentOrgInfo?.orgId"
      :is-auto-select-first-children="!orgSearchValue"
      search-input-placeholder="搜索地区、项目名称"
      @confirm="handleHeadquartersConfirm"
      :isShowTickIcon="false"
  >
    <template #curr-value>
      <div class="curr-value">
        {{ currentOrgInfo?.orgName }}
      </div>
    </template>
  </jb-mobile-tree-select-popup>

  <!-- 群工单-从下到上 -->
  <project-select
      v-model:org-id="houseId"
      v-model:visible="groupSelectPopupVisible"
      placeholder="搜索群聊名称"
      @update:data="getGroupData"
      v-model:customizeData="allGroups"
      isNeedWechatGroupNameGroups
      key-name="wechatGroupId"
      type="group"
      title="选择群聊"
  >
  </project-select>
  <!-- 选择平铺组织架构-从下到上 -->
  <project-select
      v-model:org-id="projectSelectOrgId"
      v-model:visible="selectPopupVisible"
      v-model:title="popTitle"
      @update:data='getUpData'
      v-model:customizeData="isCustomizeList"
      placeholder="搜索项目名称"
      type="project"
  ></project-select>
  <!-- 栏目输入选择 -->
  <remark-input v-model:visible="remarkInputVisible"/>
  <!-- 时间组件 -->
  <jb-mobile-time-picker
      ref="timePickerPopupRef"
      v-model:visible="isShowTimePicker"
      v-bind="attrs"
      v-model:time-picker-type="datePickerType"
      :default-value="datePickerDefaultValue"
      :with-select-tab="true"
      @confirm="handleDatePickerConfirm"
  ></jb-mobile-time-picker>
</template>

<script setup lang="ts">
// 定义组件名称，用于keep-alive
defineOptions({
  name: 'Config'
})

import textImg from '../../../assets/images/png/task-score-normal.png'
import SelectProjectPopup from '@/components/SelectProjectPopup.vue'
import {
  JbMobileSearchInput,
  JbMobileButton,
  JbMobileTimePicker,
  JbMobileTreeSelectPopup,
  useInfiniteScroll
} from 'jb-mobile-ui'
import ProjectSelect from '../components/ProjectSelect.vue'
import RemarkInput from '../components/RemarkInput.vue'
import {debounce, isUndefined, throttle} from "lodash-es";
import {
  ref,
  onMounted,
  onActivated,
  onDeactivated,
  computed,
  watch,
  nextTick,
  useAttrs,
} from 'vue'
import useScroll from '@/hooks/useScroll'
// import { ssoLogin, getIamUserId, getXuanlanUserId } from '@/api/login'
import dayjs from 'dayjs'
import {useRouter, useRoute} from 'vue-router'
import {statistics, list, finish, getUserProjectInfo, queryOrganizationRelation} from '@/api/workOrder'
// utils
import {getDataFunc, filterOrgList, getUrlState} from '@/utils/common'
import type {TOrgData} from '@/types/common'
// api
import { getIAMOrgTree } from '@/api/user'

import {storeToRefs} from 'pinia'
import {useApplicationStore} from '@/stores/application'
// import { format } from 'echarts/core'
import {useUserStore} from '@/stores/user'
import type {Ref} from "vue";
import {showToast} from "vant";
const currSelectedOrgInfo = ref() // 当前选择的对象
const groupName = ref('全部群工单')
const applicationStore = useApplicationStore()
const { currentOrgInfo, authHouseList, currentHouseIdList } = storeToRefs(applicationStore) || {}
const projectSelectOrgId = ref(currentOrgInfo.value?.orgId || '')
const taskListPaddingTop = ref(0) // taskListRef的paddingTop，如果吸顶出现了，就会加20px
interface IOrgItem {
  houseName?: string;
  houseId?: string;
  orgId?: string
}

const fromWorkSpace = ref(false) // 是否从企微工作台进来，如果从企微工作台进入则不能显示搞定了，只显示状态
// 获取组织树全部信息
const orgData = ref<TOrgData[]>([])
//判断进入（true群聊，false为组织）
const isCrowd = ref(true);

interface GroupItem {
  houseId: string;
  houseName: string;
  wechatGroupId: string;
  wechatGroupName: string;
}

const isCustomizeList = ref<number[]>([])
const allGroups = ref<GroupItem[]>([])
const groupSelectPopupVisible = ref(false);
const getSelectPopup = () => {
  groupSelectPopupVisible.value = true;
}

// 选择项目弹出层
const modelValue = ref('')
watch(modelValue, debounce((val) => {
  listParams.value = {
    ...listParams.value,
    content: val,
    currentPage: 1, // 重置页码
  }
  // 搜索时重置缓存状态
  isFromCache.value = false
  fetchWorkOrderList()
}, 300))

// 加载更多数据（向下滚动触底时调用）
const loadMoreData = async () => {
  try {
    // 增加页码
    listParams.value.currentPage = (listParams.value.currentPage as number) + 1;
    await fetchWorkOrderList(true);
  } catch (error) {
    console.log('加载更多数据失败:', error)
  }
}
// 使用无限滚动 hooks
const {
  handleScroll,
  setDataState,
  scrollInfo
} = useInfiniteScroll({
  bottomThreshold: 350,      // 距离底部150px时触发
  topThreshold: 150,          // 距离顶部150px时触发
  throttleDelay: 300,        // 300ms节流
  onLoadMore: loadMoreData,  // 向下加载回调
  debug: true                // 关闭调试日志
})
const {userInfo} = storeToRefs(useUserStore())// 用户信息
const route = useRoute();
const orgName = ref('请选择组织架构') // 当前选择的地区或项目在组织树的路径
const isShowSelectProjectPopup = ref(false) // 是否显示选择项目弹出层
const houseId = ref('-1')
const wechatGroupId = ref('')
const organizationId = ref('')
const popTitle = ref('')
const state = ref({
  isCrowd: 0,
  wechatGroupId: '',
  houseId: '',
  fromWorkSpace: false,
})
// const popPlaceholder = ref('')
// 点击确认
// const getSure = (data) => {
//   houseId.value = data[1].houseId;
//   organizationId.value = data[1].orgId;
//   listParams.value = {
//     ...listParams.value,
//     houseId: houseId.value,
//     organizationId: organizationId.value,
//   }
//   statisticsParams.value = {
//     ...listParams.value,
//     houseId: houseId.value,
//     organizationId: organizationId.value,
//   }
//   fetchWorkOrderList()
//   statisticsData()
// }
const taskStatus = ref('全部')
const categoryStatistics = ref([
  {
    categoryName: '全部',
    categoryId: -1,
    workOrderNum: 0,
  },
  {
    categoryName: '工程',
    categoryId: 6,
    workOrderNum: 0,
  },
  {
    categoryName: '工程1',
    categoryId: 2,
    workOrderNum: 0,
  },
  {
    categoryName: '工程2',
    categoryId: 3,
    workOrderNum: 0,
  },
  {
    categoryName: '工程3',
    categoryId: 4,
    workOrderNum: 0,
  },
])

// 记录每个tab是否第一次点击
const tabFirstClickMap = ref(new Map<string, boolean>())
// 记录每个tab的滚动位置 - 使用响应式变量
const tabScrollPositionMap = ref(new Map<string, number>())
const recordScrollTopPos = ref(0) // 记录滚动位置
// 保存滚动位置
const saveTabScrollPosition = (tabName: string, scrollTop: number) => {
  tabScrollPositionMap.value.set(tabName, scrollTop)
  console.log('保存滚动位置:', scrollTop, 'for tab:', tabName)
}

// 获取指定tab的滚动位置
const getTabScrollPosition = (tabName: string): number => {
  return tabScrollPositionMap.value.get(tabName) || 0
}

// 标志位：是否正在恢复滚动位置
const isRestoringScroll = ref(false)

// 防抖保存滚动位置
const debouncedSaveScrollPosition = debounce(() => {
  // 如果正在恢复滚动位置，则不保存
  if (isRestoringScroll.value) {
    console.log('正在恢复滚动位置，跳过保存')
    return
  }

  const currentTabName = taskStatus.value
  if (currentTabName && targetRef.value) {
    const scrollTop = targetRef.value.scrollTop
    console.log('滚动时实时保存位置，currentTab:', currentTabName, 'scrollTop:', scrollTop)
    saveTabScrollPosition(currentTabName, scrollTop)
  }
}, 500) // 500ms防抖

// 综合滚动处理函数
const onScroll = throttle((event: Event) => {
  const target = event.target as HTMLElement;
  const scrollTop = target.scrollTop; // ✅ OK
  if (scrollTop === 0) {
    scrollToFixed.value = false
  }
  // 处理无限滚动
  handleScroll(event)
  // 保存滚动位置
  debouncedSaveScrollPosition()
}, 300)

const isShowTimePicker = ref(false)//当前时间选择
const targetRef = ref() // task容器的ref
const middleRef = ref<Record<string, string | number>>({})
const scrollToFixed = ref(false)
watch(scrollToFixed, (val) => {
  if (val) {
    return taskListPaddingTop.value = 20
  }
  taskListPaddingTop.value = 0
})
const attrs = useAttrs()
const datePickerType = ref('dateRange')
const datePickerDefaultValue = ref(dayjs().subtract(0, 'month').toDate())
const cardImg = ref(true)
const {updateThreshold} = useScroll({
  className: 'task',
  onReach: (flag: boolean) => {
    scrollToFixed.value = flag
  },
})
const changeTab = (value: string, id: string) => {
  console.log('value', value)
  // 记录当前tab的滚动位置（切换前）
  const currentTab = taskStatus.value
  console.log('currentTab', currentTab)
  if (currentTab && targetRef.value && !isRestoringScroll.value) {
    const scrollTop = targetRef.value.scrollTop
    saveTabScrollPosition(currentTab, scrollTop)
  }

  const match = categoryStatistics.value.find(item => item.categoryName === value)
  console.log('match', match)
  listParams.value = {
    ...listParams.value,
    categoryId: match!.categoryId < 0 ? undefined : match?.categoryId,
    currentPage: 1, // 重置页码
  }

  // 检查是否第一次点击该tab
  const isFirstClick = !tabFirstClickMap.value.has(value)
  if (isFirstClick) {
    tabFirstClickMap.value.set(value, true)
  }

  // 主动切换tab时重置缓存状态
  isFromCache.value = false
  fetchWorkOrderList() // 不是加载更多

  // 在tab切换后确保重新计算高度
  nextTick(() => {
    recalculateTaskListHeight()
  })
  // handleConfirmFilter();
}
const startTime = ref('');
const endTime = ref('');
let flag = true // 用来控制第一次进来不要请求
// 确认选择某个时间范围
const handleDatePickerConfirm = (data: Record<string, any>, time?: string[]) => {
  currentTime.value = data.dateValueFormat
  const [startDate, endDate] = data.value.map(dateTimeStr => dateTimeStr.split(' ')[0]);
  // const state = route.query.state
  // let stateObj: any = {}
  // if (state && typeof state === 'string' && state.indexOf('?') > -1) {
  //   const jsonPart = state.slice(0, state.indexOf('?'));
  //   // const queryPart = state.slice(state.indexOf('?') + 1);
  //   stateObj = JSON.parse(decodeURIComponent(jsonPart));
  // } else if (state && typeof state === 'string') {
  //   stateObj = JSON.parse(decodeURIComponent(state));
  // }
  const stateObj = getUrlState(route)
  const {wechatGroupId: wId, houseId: hId} = stateObj || {};
  listParams.value = {
    ...listParams.value,
    wechatGroupId: wId,
    houseId: hId,
  }
  cardImg.value = (startDate === endDate) && (startDate === formatDate(new Date()));
  startTime.value = data.value[0];
  endTime.value = data.value[1];

  // 重置页码
  listParams.value.currentPage = 1;
  if (flag) {
    return
  }
  fetchWorkOrderList();
  statisticsData();
}
const currentTime = ref('')
const selectPopupVisible = ref(false)
const remarkInputVisible = ref(false)
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}
const router = useRouter()
// 点击详情
const getDetail = ((item: any) => {
  console.log('item', item)
  recordScrollTopPos.value = scrollInfo.value?.scrollTop
  const feedback = ref(0);
  if (isCrowd.value) feedback.value = 1;
  router.push({path: '/detail', query: {id: item.workOrderId, isCrowd: feedback.value}});
})
watch(orgData, async (val) => {
  if (val?.length) {
    await getOrgList()
    await handleQueryOrganizationRelation(getRealHouseId.value);
    
    // 只有在列表为空且不是来自缓存时才重新加载数据，避免 keep-alive 缓存的数据被重置
    if (orderList.value.length === 0 && !isFromCache.value) {
      console.log('首次加载数据或数据为空，开始获取列表数据')
      await fetchWorkOrderList();
      await statisticsData();
    } else if (isFromCache.value) {
      console.log('检测到缓存数据，跳过数据重新加载')
    }
    flag = false
  }
}, { immediate: true })
const lastDayFinishes = ref([])
const recentDays = computed(() => {
  const days = [];
  const today = new Date();

  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() - i);

    const formattedDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;

    // 查找匹配的完成数据
    const matchedFinish = lastDayFinishes.value.find(
        item => item.finishDay === formattedDate
    );

    let dayStr = {
      total: matchedFinish ? matchedFinish.total : 0,
      title: '',
      cls: '',
      name: formattedDate // 使用统一格式的日期字符串
    };

    if (i === 0) {
      dayStr.title = '今日';
      dayStr.cls = 'highlight';
    } else if (i === 1) {
      dayStr.title = '昨日';
    } else {
      dayStr.title = `${date.getDate()}日`;
    }
    days.push(dayStr);
  }
  return days.reverse();
});
// const orgOriginalList = ref()
const userStore = useUserStore()
const { orgInfoList } = storeToRefs(userStore) || {} // 当前用户拥有的所有权限的项目
// const orgInfo = orgInfoList.value?.[0] // 当前用户所在的组织架构

interface HouseItem {
  houseId: number;
  houseName: string;
  wechatGroupId: string | null;
  wechatGroupName: string | null;
  organizationRelationVoList: [] | null;
}

// 尝试着加载默认数据
const loadDefaultData = () => {
  const {houseId, orgId: oId, orgName: oName} = currentOrgInfo.value || {}
  // console.log('houseId', orgId, orgName)
  if (currentOrgInfo && Object.keys(currentOrgInfo)?.length) {
    orgName.value = oName || ''
    listParams.value = {
      ...listParams.value,
      houseId: houseId || '',
      organizationId: oId || ''
    }
    statisticsParams.value = {
      ...statisticsParams.value,
      houseId: houseId || '',
      organizationId: oId || ''
    }
  }
}
const isHasHeadquarters = ref(false)
const isShowHeadquartersPopup = ref(false) // 是否显示总部组织架构弹窗
// const orgTreeData = ref([]) // 存储getIAMOrgTree返回的原始数据
const orgSearchValue = ref('') // 搜索值
const handleQueryOrganizationRelation = async (houseList: string[] | number[]) => {
  console.log('handleQueryOrganizationRelation')
  const result = await queryOrganizationRelation(houseList); // 获取组织与群聊关系
  const { data: groupList } = result.data || {}
  authHouseList.value = houseList // 把当前有所有权限的放到localStorage
  allGroups.value = groupList?.flatMap((item: HouseItem) => item.organizationRelationVoList) || [];

  allGroups.value.unshift({
    houseId: '-1',
    houseName: '',
    wechatGroupId: '',
    wechatGroupName: '全部群工单'
  })
  console.log('allGroups.value', allGroups.value)
  houseId.value = '-1'
  const {isCrowd: isCrowdState, wechatGroupId: wechatGroupIdState} = state.value || {}
  if (isCrowdState === 1 && !wechatGroupIdState) {
    groupName.value = '全部群工单'
  }
  wechatGroupId.value = ''
}
// const defaultOrgId = ref() // 默认选中的组织ID
// 获取组织架构列表
const getOrgList = async () => {
  const res = await getUserProjectInfo({userId: userInfo.value.userId,});
  const { data } = res.data || {}
  const orgIds = data?.map((item: IOrgItem) => item.orgId);
  const houseList = data?.map((item: IOrgItem) => item.houseId);
  isHasHeadquarters.value = orgIds.includes(19759);
  // console.log('isHasHeadquarters', isHasHeadquarters.value, currentOrgInfo.value?.orgId);

  if (!isHasHeadquarters.value && currentOrgInfo.value?.orgId === 19759) {
    if (orgData.value && data[0]?.orgId) {
      const headquartersList = filterOrgList(orgData.value, data[0]?.orgId)
      const headquarters = headquartersList && headquartersList.length > 0 ? headquartersList[0] : null
      if (headquarters) {
        applicationStore.setCurrentOrgInfo(headquarters)
      } else {
        console.warn('未找到节点，未设置currentOrgInfo')
      }
    } else {
      console.warn('orgData或orgId为空，未设置currentOrgInfo')
    }
  }
  // const matchedOrgs = findMatchingOrgs(orgData.value, orgIds);
  // console.log(matchedOrgs,'matchedOrgs',orgData.value, orgIds);
  const matchedOrgs = findNodesByIds(orgData.value, orgIds)
  const defaultSelectedStr = localStorage.getItem('default_selected') // 保存选中的houseId以及orgId
  if (defaultSelectedStr) {
    const defaultSelected = JSON.parse(defaultSelectedStr)
    orgName.value = defaultSelected.orgName
    listParams.value = {
      ...listParams.value,
      houseId: defaultSelected?.houseId || houseId.value,
      organizationId: defaultSelected?.orgId || organizationId.value
    }
    statisticsParams.value = {
      ...statisticsParams.value,
      houseId: defaultSelected?.houseId || houseId.value,
      organizationId: defaultSelected?.orgId || organizationId.value
    }
  }
  isCustomizeList.value = matchedOrgs || []
  // orgName.value = matchedOrgs[0].orgName
  if (flag) {
    listParams.value = {
      ...listParams.value,
      houseIds: houseList,
      organizationId: undefined
    }
  }
  isCustomizeList.value = findNodesByIds(orgData.value, orgIds) || []
};

// 处理SelectProjectPopup传递的组织架构数据
const handleSelectProjectData = (data: TOrgData[]) => {
  // orgTreeData.value = data || []
}

// 总部组织架构选择确认
const handleHeadquartersConfirm = async (value: TOrgData[]) => {
  let houseIds = [] // 项目id集合
  // console.log('value', value)
  if (value && value.length > 0) {
    const selectedOrg = value[1].orgId === -1 ? value[0] : value[1] // 如果第二项的 orgId 为 -1 则表示选择的是地区，否则则是选择的是项目
    // console.log('selectedOrg', selectedOrg)
    currSelectedOrgInfo.value = selectedOrg
    /**
     * 19759是集团的orgId，如果是集团的,就需要获取所有的houseUuidList
     * */
    if (selectedOrg.orgId === 19759) {
      houseIds = orgInfoList.value?.length &&
          orgInfoList.value.filter(item => item.houseId && item.houseId !== '0')?.map(item => item.houseId) || []
    } else {
      houseIds = selectedOrg && selectedOrg?.child?.length ?
          selectedOrg.child?.filter(item => item.houseId)?.map(item => item.houseId)
          : [selectedOrg.houseId]
    }

    currSelectedOrgInfo.value.houseIds = houseIds
    // currentHouseIdList.value = houseIds
    // if (selectedOrg && Object.keys(selectedOrg)?.length && selectedOrg.orgId === 19759) {
    //   // currentOrgInfo.value!.houseIds = authHouseList
    //   currSelectedOrgInfo.value = {
    //     ...currentOrgInfo.value,
    //     // ...selectedOrg,
    //     // houseIds: authHouseList.value
    //   }
    // }
    currentHouseIdList.value = houseIds as number[]
    // 是集团权限的，将
    orgName.value = selectedOrg.orgName
    houseId.value = selectedOrg.houseId?.toString() || ''
    organizationId.value = selectedOrg.orgId?.toString() || ''
    // 更新查询参数
    listParams.value = {
      ...listParams.value,
      houseId: houseId.value === '0' ? undefined : houseId.value,
      houseIds
      // organizationId: organizationId.value,
    }
    statisticsParams.value = {
      ...statisticsParams.value,
      houseId: houseId.value === '0' ? undefined : houseId.value,
      houseIds
      // organizationId: organizationId.value,
    }
    // 重新获取数据
    listParams.value.currentPage = 1; // 重置页码
    await handleQueryOrganizationRelation(getRealHouseId.value) // 重新请求群聊
    await fetchWorkOrderList()
    await statisticsData()
    applicationStore.setCurrentOrgInfo(selectedOrg)
    // 保存选择到本地存储
    localStorage.setItem('default_selected', JSON.stringify({
      orgName: selectedOrg.orgName,
      houseId: selectedOrg.houseId,
      orgId: selectedOrg.orgId
    }))
  }
  isShowHeadquartersPopup.value = false
}

// const findMatchingOrgs = (list, orgIds) => {
//   const result = ref([]); // 使用 ref 让结果响应式
//   const traverse = (nodes) => {
//     if (!nodes || !nodes.length) return;
//     for (const node of nodes) {
//       if (orgIds.includes(node.orgId)) {
//         result.value.push(node); // 注意：操作 ref 时要用 .value
//       }
//       if (node.child?.length) { // 可选链简化判断
//         traverse(node.child);
//       }
//     }
//   };}
// 调用工单列表接口
/**
 * 经典嘢，以后边个写呢种代码拖出去斩
 * */
const listParams = ref<Record<string, any>>({
  currentPage: 1,
  pageSize: 20,
  content: '',
  status: '',
  categoryId: undefined,
  wechatGroupId: wechatGroupId.value,
  houseId: houseId.value,
  organizationId: organizationId.value,
  startDate: startTime.value,
  endDate: endTime.value,
})
//调用统计工单
const statisticsParams = ref<Record<string, any>>({
  wechatGroupId: wechatGroupId.value,
  houseId: houseId.value,
  organizationId: organizationId.value,
  startDate: startTime,
  endDate: endTime,
})
interface IOrderListItem {
  workOrderId: string;
  categoryName: string;
  categoryId: string | number;
  status: string | number;
  createTime: string;
  finishTime: string | null;
  userName: string;
  phoneNumber: string;
  content: string;
  workOrderTime: string;
}

const orderList = ref<IOrderListItem[]>([])
// 数据加载状态标志位
const isDataLoaded = ref(false) // 标记数据是否已经加载过
const isFromCache = ref(false)  // 标记是否来自缓存

/**
 * 以下两个接口都需要注意，第一个是fetchWorkOrderList、第二个是statisticsData
 * 这两个接口都有相同的参数需要传，listParams里面的organzationId和houseId需要动态获取,其中地区公司是没有houseId,或者houseId === '0'时，organizationId需要传,如果有houseId
 * 就不用传organzationId了,还有一点需要注意，如果选择项目，但是假如不得不传orgId的，需要传当前项目所在的地区公司的orgId而不是自己的orgId
 * */
// 判断有多个houseId需要用哪个的逻辑
const getRealHouseId = computed(() => {
  // const { houseId: hId } = state.value;
  // // state是路由传过来的，如果houseId有值，则直接返回houseId
  // if (hId) {
  //   return [hId]
  // }
  if (currentHouseIdList.value?.length) {
    return currentHouseIdList.value
  }
  const { houseIds } = currSelectedOrgInfo.value || {}
  // const { houseId: listParamsHouseId } = listParams.value || {}
  return houseIds && houseIds?.length ? houseIds : authHouseList.value
})
// 请求工单列表
const fetchWorkOrderList = async (isLoadMore = false) => {
  try {
    const res = await list({
      ...listParams.value,
      startDate: startTime.value || '',
      endDate: endTime.value || '',
      houseId: undefined,
      houseIds: getRealHouseId.value,
      // houseIds: houseId ? [houseId] : authHouseList.value,
      organizationId: undefined
      // organizationId: isCrowd ? undefined : (houseId && houseId !== '0' ? undefined : (orgId || organizationId))
    });

    const newData = res.data.data.result || [];
    if (isLoadMore) {
      // 加载更多时追加数据，需要去重
      const existingIds = new Set(orderList.value.map(item => item.workOrderId));
      const uniqueNewData = newData.filter((item: IOrderListItem) => !existingIds.has(item.workOrderId));
      orderList.value = [...orderList.value, ...uniqueNewData];
    } else {
      // 首次加载或刷新时替换数据
      orderList.value = newData;
      isDataLoaded.value = true; // 标记数据已加载
    }

    // 数据更新后重新计算高度
    nextTick(() => {
      recalculateTaskListHeight()
    })

    setDataState({hasMore: newData.length === listParams.value?.pageSize});
  } catch (error) {
    console.error('请求失败:', error);
    // 请求失败时回退页码
    if (isLoadMore) {
      listParams.value.currentPage = (listParams.value.currentPage as number) - 1;
    }
  }
};
// 获取看板数据
const statisticsData = async () => {
  try {
    const { wechatGroupId } = listParams.value || {}
    const res = await statistics({
      wechatGroupId,
      // houseIds: houseId ? [houseId] : authHouseList.value,
      startDate: startTime.value,
      endDate: endTime.value,
      houseIds: getRealHouseId.value
    });
    workOrderData.value.finishRate = res.data.data.finishRate;
    workOrderData.value.totalNum = res.data.data.totalNum;
    const sun = ref(0)
    sun.value = res.data.data.categoryStatistics.reduce((total: number, item: any) => {
      return total + Number(item.workOrderNum);
    }, 0);
    categoryStatistics.value = res.data.data.categoryStatistics
    if (categoryStatistics.value.length > 0) (
        categoryStatistics.value.unshift({
          categoryName: '全部',
          categoryId: -1,
          workOrderNum: sun.value,
        })
    )
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月份从 0 开始
    const day = date.getDate();
    const formattedDate = `${year}年${month}月${day}日`;
    if (formattedDate == currentTime.value) lastDayFinishes.value = res.data.data.lastDayFinishes

  } catch (error) {
  }
}

const workOrderData = ref({
  finishRate: '',
  totalNum: ''
})

// 点击分类搜索
const classification = async (val: string) => {
  statisticsParams.value = {
    ...statisticsParams.value,
  }
  await getFinish()
}

// 完成工单
const getFinishParams = ref({
  workOrderId: '',
  userId: userInfo.value.userId,
  userName: userInfo.value.userName
})
const getFinish = async () => {
  try {
    const {data} = await finish({...getFinishParams.value}) || {};
    if (data.code === '00000') {
      const {workOrderId} = getFinishParams.value || {}
      if (orderList.value) {
        orderList.value.forEach((item) => {
          if (item.workOrderId === workOrderId) {
            item.status = 3; // 已完成状态
          }
        })
      }
      showToast('工单已完成');
    }
  } catch (error) {
    showToast('修改失败')
  }
}
const getWorkOrder = async (val: any) => {
  try {
    getFinishParams.value = {
      ...getFinishParams.value,
      workOrderId: val.workOrderId
    }
    await getFinish();
  } catch (error) {
  }
}

// const idx = ref(1)
// const itemName = ref('')
// const groupChat = ref('')
const getUpData = async (val: any) => {
  console.log('getUpdata',val);
  const {houseId: hId, orgId, orgName: oName} = val || {}
  await handleQueryOrganizationRelation([hId])
  currentOrgInfo.value = {...currentOrgInfo.value, ...val}
  // 移除 houseId.value = hId，避免项目选择影响群工单的 houseId 变量
  listParams.value = {
    ...listParams.value,
    houseId: hId,
    currentPage: 1, // 重置页码
  }
  statisticsParams.value = {
    ...statisticsParams.value,
    organizationId: orgId || '',
    houseId: hId,
  }
  orgName.value = oName
  await statisticsData();
  await fetchWorkOrderList(); // 不是加载更多
}
const getGroupData = async (val: any) => {
  // console.log(val, 'getGroupData');
  const {wechatGroupName, wechatGroupId} = val || {}
  listParams.value = {
    ...listParams.value,
    wechatGroupId,
    currentPage: 1, // 重置页码
  }
  groupName.value = wechatGroupId ? wechatGroupName : '全部群工单'
  await statisticsData();
  await fetchWorkOrderList(); // 不是加载更多
}

// 获取项目接口

/**
 * 根据orgIds查找组织树中匹配的节点，收集匹配节点及其所有子孙节点的houseId值
 * @param {Array} treeData - 组织树数据
 * @param {Array<string|number>} targetIds - 要查询的orgId数组
 * @returns {Array<number>} - 匹配节点及其子孙节点中houseId不为0的值组成的一维数组
 */
function findNodesByIds(treeData: any[], targetIds: Array<string | number>): number[] {
  // 将目标ID数组转换为Set以便快速查找
  const idSet = new Set(targetIds);
  const houseIds: any[] = [];
  // 使用Set来追踪已收集的houseId，避免重复
  const collectedHouseIds = new Set<number>();

  /**
   * 递归收集节点及其所有子孙节点的houseId
   * @param {Object} node - 当前节点
   */
  function collectHouseIds(node: any): void {
    // 收集当前节点的houseId（如果不为0且未收集过）
    if (node.houseId && node.houseId !== 0 && node.orgId !== 19759 && !collectedHouseIds.has(node.houseId)) {
      houseIds.push(node);
      collectedHouseIds.add(node.houseId);
    }
    // 递归收集子节点的houseId
    if (node.child && node.child.length > 0) {
      for (const child of node.child) {
        collectHouseIds(child);
      }
    }
  }

  /**
   * 递归遍历树节点查找匹配的orgId
   * @param {Object} node - 当前节点
   */
  function traverse(node: any): void {
    // 如果当前节点的orgId在目标数组中
    if (idSet.has(node.orgId)) {
      // 收集该节点及其所有子孙节点的houseId
      collectHouseIds(node);
    }

    // 继续递归遍历子节点寻找其他匹配的orgId
    if (node.child && node.child.length > 0) {
      for (const child of node.child) {
        traverse(child);
      }
    }
  }

  // 遍历整个树结构
  for (const node of treeData) {
    traverse(node);
  }

  // 去重并返回结果
  return Array.from(new Set(houseIds));
}
loadDefaultData()

// 处理选择弹出层显示
const handleSelectProjectPopupVisible = () => {
  if (!isHasHeadquarters.value) {
    selectPopupVisible.value = true
  } else {
    // 当有总部权限时，显示总部组织架构弹窗
    isShowHeadquartersPopup.value = true
  }
}
const filteredOrgList = ref() // 过滤后的组织架构列表

// 将第一层和第二层数据放在同一层级的处理函数
const flattenOrgData = (data: TOrgData[]): TOrgData[] => {
  if (!data || data.length === 0) return []

  const result: TOrgData[] = []

  for (const firstLevelNode of data) {
    // 只有当第一层节点有子节点时才添加第一层节点
    if (firstLevelNode.child && firstLevelNode.child.length > 0) {
      // 添加第一层节点，清空其child避免重复显示
      const firstNode: TOrgData = {
        ...firstLevelNode,
        child: []
      }
      result.push(firstNode)

      // 将第二层节点提升到第一层（只有当第二层节点有子节点时才添加）
      for (const secondLevelNode of firstLevelNode.child) {
        // 只有当第二层节点有子节点（第三层数据）时才添加到结果中
        if (secondLevelNode.child && secondLevelNode.child.length > 0) {
          // 第二层节点的子节点（原第三层）成为新的第二层
          const promotedNode: TOrgData = {
            ...secondLevelNode,
            child: secondLevelNode.child || []
          }
          result.push(promotedNode)
        }
      }
    }
    // 如果第一层节点没有子节点，则跳过不显示该节点
  }

  return result
}
// 过滤组织架构列表
const orgList = computed(() => {
  return orgSearchValue.value ? filteredOrgList.value : orgData.value
  // return flattenOrgData(baseData || [])
})
watch(() => orgSearchValue.value, debounce((newVal) => {
  if (newVal) {
    filteredOrgList.value = filterOrgList(orgData.value, newVal)
  } else {
    filteredOrgList.value = null
  }
}))
// 当搜索有焦点时，滚动到吸顶效果出现的位置
const handleInputFocus = () => {
  // 滚动到吸顶效果出现的位置
  const threshold = Number(middleRef.value?.clientHeight) || 0
  scrollToFixed.value = true
  if (targetRef.value && threshold > 0) {
    requestAnimationFrame(() => {
      console.log('1222')
      targetRef.value.scrollTo({
        top: threshold + 30,
        behavior: 'smooth'
      })
    })
    nextTick(() => {
      console.log('333')
    })
  }
}
const taskListRef = ref() // 任务列表的ref
const taskListHeight = ref(0) // 任务列表的高度

// 重新计算任务列表高度的函数
const recalculateTaskListHeight = () => {
  setTimeout(() => {
    const itemCount = orderList.value.length
    const itemHeight = 118 // 每个job item的实际高度(108px height + 10px margin-bottom)
    const paddingTop = taskListPaddingTop.value // 动态的paddingTop
    const listPadding = 20 // task-list的上下padding (10px * 2)
    const footerHeight = itemCount > 0 ? 103 : 0 // task-footer高度 (26px上padding + 59px下padding + 18px line-height)
    const emptyStateHeight = itemCount === 0 ? 120 : 0 // empty-state高度 (40px padding * 2 + 40px文字高度)
    
    // 计算实际需要的高度
    let calculatedHeight = paddingTop + listPadding + emptyStateHeight
    
    if (itemCount > 0) {
      // 有数据时：items高度 + footer高度
      calculatedHeight += (itemCount * itemHeight) + footerHeight
    }
    
    // 设置合理的最小高度，确保页面布局正常，同时避免过度留白
    const minHeight = Math.max(400, calculatedHeight)
    taskListHeight.value = minHeight
    
    console.log('高度计算详情:', {
      itemCount,
      itemHeight,
      paddingTop,
      listPadding,
      footerHeight,
      emptyStateHeight,
      calculatedHeight,
      finalHeight: taskListHeight.value
    })
    
    // 数据重新渲染后，恢复滚动位置
    restoreScrollPosition()
  }, 50) // 稍微延迟确保DOM更新完成
}

// 恢复滚动位置的函数
const restoreScrollPosition = () => {
  setTimeout(() => {
    const currentTabName = taskStatus.value
    if (currentTabName && targetRef.value) {
      const savedScrollTop = getTabScrollPosition(currentTabName)
      if (savedScrollTop > 0) {
        console.log('数据渲染后恢复滚动位置:', savedScrollTop, 'for tab:', currentTabName)

        // 设置恢复标志位
        isRestoringScroll.value = true

        targetRef.value.scrollTo({
          top: savedScrollTop,
          behavior: 'instant' // 使用instant避免动画影响
        })

        // 恢复完成后清除标志位
        setTimeout(() => {
          isRestoringScroll.value = false
          console.log('滚动位置恢复完成，重新启用保存机制')
        }, 100)
      }
    }
  }, 100) // 延迟确保高度计算完成
}

// 监听orderList变化，自动重新计算高度
watch(orderList, (newList, oldList) => {
  // 只有当列表内容真正变化时才重新计算
  if (JSON.stringify(newList) !== JSON.stringify(oldList)) {
    recalculateTaskListHeight()
  }
}, {deep: true})

// 计算任务列表的动态高度（CSS v-bind需要computed property才能响应式更新）
const computedTaskListHeight = computed(() => {
  // 直接使用计算出的高度，不再强制设置最小高度
  const height = taskListHeight.value
  console.log('computed height:', height, 'item count:', orderList.value.length)
  return height
})

// 文本截取工具函数，精确控制字符数量
const truncateText = (text: string, maxLength: number = 8): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

// 计算截取后的组织名称（用于company-name）
const truncatedCompanyName = computed(() => {
  if (isCrowd.value) {
    return truncateText(groupName.value)
  } else {
    const displayName = currentOrgInfo.value?.orgName || orgName.value || '请选择组织架构'
    return truncateText(displayName)
  }
})

// 计算截取后的群组名称（用于task-header__right）
const truncatedGroupName = computed(() => {
  return truncateText(groupName.value)
})
// 获取组织架构列表
const getOrgData = async() => {
  const data = getDataFunc(await getIAMOrgTree())
  if (data?.length) {
    const headquarters: TOrgData = data[0]
    const dataList: TOrgData[] = headquarters?.child?.filter((item: Record<string, any>) => item.child?.length > 0) || []
    dataList.unshift({
      ...headquarters,
      orgName: '金碧物业',
      orgId: 19759,
      child: []
    })
    dataList.forEach((item: Record<string, any>) => {
      item.child.unshift({
        orgName: '全部',
        orgId: -1
      })
    })
    orgData.value = dataList
  }
}

onMounted(async () => {
  updateThreshold(middleRef.value?.clientHeight as number);

  // 初始化时设置默认高度
  taskListHeight.value = 400;
  if (route.query.isCrowd) {
    isCrowd.value = !!parseInt(route.query?.isCrowd as string)
  }
  const stateObj = getUrlState(route)
  state.value = stateObj;
  if (state.value?.houseId && state.value?.houseId !== '0') {
    currentHouseIdList.value = [state.value?.houseId]
  }
  // console.log('state.value', state.value)
  // console.log('currSelectedOrgInfo.value', currSelectedOrgInfo.value)
  // console.log('currentHouseIdList.value', currentHouseIdList.value)
  // console.log('stateObj', stateObj, 'route.query', route.query)
  const {wechatGroupId: wId, houseId: hId, fromWorkSpace: fws, isCrowd: ic} = stateObj || {}
  fromWorkSpace.value = !!fws
  isCrowd.value = !!ic
  // console.log('isCrowd', isCrowd)
  if (wId) {
    wechatGroupId.value = wId as string;
    houseId.value = hId as string;
    listParams.value = {
      ...listParams.value,
      wechatGroupId: wId,
      houseId: hId,
    }
    const res = await queryOrganizationRelation([houseId.value])
    const {code, data} = res.data || {}
    let match = null
    if (code === '00000') {
      data.map((item: any) => {
        if (item.houseId === parseInt(houseId.value)) {
          if (item.organizationRelationVoList?.length) {
            match = item.organizationRelationVoList.find((rItem: any) => rItem.wechatGroupId === wechatGroupId.value)
            if (match) {
              groupName.value = match.wechatGroupName;
              return
            }
          }
        }
      })
    }
    // groupName.value = route.query.wechatGroupId;
  }
});

// keep-alive组件激活时恢复当前tab的滚动位置
onActivated(async () => {
  console.log('页面激活 - keep-alive 缓存状态:', {
    hasOrgData: orgData.value?.length > 0,
    hasOrderData: orderList.value.length > 0,
    isDataLoaded: isDataLoaded.value
  })
  
  // 检查是否来自缓存
  isFromCache.value = orderList.value.length > 0 && isDataLoaded.value
  
  // 只有当 orgData 为空时才重新获取，避免不必要的数据重置
  if (!orgData.value || orgData.value.length === 0) {
    console.log('重新获取组织架构数据')
    await getOrgData()
  } else {
    console.log('使用缓存的组织架构数据')
  }
  
  if (recordScrollTopPos.value) {
    requestAnimationFrame(() => {
      targetRef.value?.scrollTo({
        top: recordScrollTopPos.value,
        behavior: 'smooth'
      })
    })
  }
  
  // 如果是来自缓存，直接恢复滚动位置
  if (isFromCache.value) {
    console.log('从缓存恢复，当前列表数据量:', orderList.value.length)
    nextTick(() => {
      recalculateTaskListHeight()
    })
  }
});

</script>

<style lang="less" scoped>
.curr-value {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 16px;
  color: #2B2F33;
  display: flex;
  align-items: center;
  margin-left: 16px;
  height: 100%;
}

.task {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 48px;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  &-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    width: 100%;
    height: 368px;
    background-image: linear-gradient(179deg, #ff6a00 0%, #ffec85 66%);
  }

  &-header {
    position: fixed;
    z-index: 2;
    top: 0;
    width: 100%;
    height: 48px;
    background-color: rgba(0, 0, 0, 0);
    padding: 8px 10px 8px 16px;
    box-sizing: border-box;
    font-weight: 500;
    font-size: 16px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    // font-family: base-font-family;
    &.fixed {
      background: #ffffff;
      color: var(--base-font-color) !important;
      z-index: 9999 !important;
    }

    &__left {
      width: 243px;
      display: flex;
      align-items: center;
    }

    &__right {
      height: 32px;
      background: none;
      border-radius: 16px;
      font-size: 14px;
      line-height: 32px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: inherit;
      .svg-icon {
        margin-left: 4px;
      }
    }

    .company {
      &-name {
        margin: 0 4px 0 6px;
        max-width: 220px;
        font-weight: 500 !important;
        font-size: 16px !important;
      }

      &-icon {
        font-size: 16px;
        color: #fff;
      }
    }
  }

  &-middle {
    padding: 0 10px 16px 10px;
    z-index: 100;
  }

  &-situation {
    height: 126px;
    box-sizing: border-box;
    padding: 16px 16px;
    background: #ffffff;
    border-radius: 12px;
    display: flex;
    background: url('@/assets/images/svg/task-bg.svg') no-repeat;
    background-size: cover;
    flex-direction: column;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      width: 134px;
      height: 134px;
      right: 0;
      top: -8px;
      background: url('@/assets/images/common/thumbs-up.png');
      background-size: cover;
    }

    &__filter {
      font-weight: 400;
      font-size: 14px;
      color: #2b2f33;
      display: flex;
      align-items: center;
    }

    &__content {
      display: flex;
      margin-top: 21px;
      width: 230px;
    }

    .card {
      flex: 1;
      display: flex;
      flex-direction: column;

      &-label {
        height: 17px;
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        display: flex;
        align-items: center;

        img {
          margin-right: 4px;
          width: 26px;
          height: 14px;
        }
      }

      &-count {
        margin-top: 7px;
        font-size: 24px;
        line-height: 29px;
        color: #ff6824;
        font-family: D-DIN-PRO-Bold;
        font-weight: 600;
      }

      &-date {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        margin-top: 6px;
      }
    }
  }

  &-finished {
    width: 100%;
    height: 137px;
    box-sizing: border-box;
    padding: 12px 16px;
    background: #ffffff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    margin-top: 10px;

    &__label {
      height: 22px;
      font-weight: 500;
      font-size: 16px;
      color: #2b2f33;
    }

    &__item {
      margin-top: 10px;
      display: flex;
      justify-content: space-around;

      .item {
        &:nth-last-child {
          margin-right: 0;
        }

        &-top {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 43px;
          height: 60px;
          background: #f7f7f7;
          border-radius: 12px;

          .count {
            margin-top: 8px;
            line-height: 17px;
            font-family: D-DIN-PRO;
            font-weight: 600;
            font-size: 14px;
            color: #c9c9c9;
          }
        }

        &-bottom {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          margin-top: 4px;
          text-align: center;
        }
      }
    }
  }

  .highlight {
    background: linear-gradient(to bottom, #FF4738, #FF8200);

    .count {
      color: #fff;
    }
  }

  &-search {
    position: sticky;
    top: 0;
    left: 0;
    z-index: 1;
    padding: 16px 15px 10px 16px;
    background: linear-gradient(180deg, #fff 0%, #F5F6F8 150%);
    border-radius: 20px 20px 0 0;
    height: 100px;
    &.fixed {
      border-radius: 0;
    }

    .jb-mobile-search-input {
      height: 35px;
    }

    :deep(.jb-mobile-button) {
      height: 31px;
      margin-top: 2px;
    }

    .tab {
      margin-top: 10px;
      margin-bottom: 10px;
      /* height: 54px; */
      :deep(.van-tabs__nav) {
        background-color: transparent;
      }
      :deep(.van-tab__text) {
        padding: 0 16px;
        line-height: 28px;
        font-weight: 400;
        font-size: 14px;
        color: var(--base-font-color);
        height: 28px;
        background: #F4F4F4;
        border-radius: 16px;
      }

      :deep(.van-tab--shrink) {
        padding: 0;
        margin-right: 8px;
        &:last-child {
          margin-right: 10px;
        }
      }

      :deep(.van-tab--active) {
        .van-tab__text {
          background: #FFF7F3;
          border: 1px solid #FF6824;
          color: #FF6824;
        }
      }

      :deep(.van-tabs__line) {
        display: none;
      }

      :deep(.van-tabs__nav--complete) {
        padding: 0;
      }

      :deep(.van-tabs__wrap) {
        height: 32px;
      }
    }
  }

  &-list {
    padding: 0 10px;
    box-sizing: border-box;
    flex: none; /* 改为 none，让高度由内联样式控制 */
    min-height: 400px; /* 减少最小高度，避免过多留白 */
    background: #fbf9f9;
    .job {
      margin-bottom: 10px;
      display: flex;
      align-items: flex-start;
      height: 108px;
      background: #fff;
      border-radius: 12px;
      padding: 12px;
      box-sizing: border-box;
      position: relative;
      &-img {
        width: 48px;
        height: 48px;
        background: #f4f4f4;
        border-radius: 12px;
      }

      &-content {
        flex: 1;
        margin-left: 10px;

        &__title {
          // margin-top: 3px;
          min-height: 22px;
          max-height: 44px;
          font-weight: 600;
          font-size: 16px;
          color: #2b2f33;
          max-width: 273px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          word-break: break-all;
          overflow:hidden;
        }

        &__time {
          margin-top: 8px;
          line-height: 17px;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
        }
      }

      &-operate {
      }
    }

    .empty-state {
      padding: 40px 0;
      text-align: center;
      color: #999999;
      font-size: 14px;
    }
  }

  &-footer {
    padding: 26px 0 59px 0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    font-weight: 400;
    font-size: 12px;
    color: #c9c9c9;
    line-height: 18px;
  }

  .task-situation__content {
    position: relative;

    .task-demarcation {
      display: block;
      top: -15px;
      position: absolute;
      width: 100%;
      height: 1px;
      color: #E1E1E3;
      opacity: 40%;
      background-color: #E1E1E3;
    }
  }

  .job-operate {
    background: #F4F4F4 !important;
    color: #999999 !important;
    position: absolute;
    right: 12px;
    bottom: 12px;
    padding-left: 16px;
    padding-right: 16px;
  }

  .job-operate11 {
    background-color: linear-gradient(90deg, #ff8200 1%, #ff4738 100%);
    background: linear-gradient(90deg, #ff8200 1%, #ff4738 100%) !important;
    color: #fff !important;
  }

  .job-operate12 {
    background: #FFF3E3 !important;
    color: #FF970F !important;
  }

  .company-name {
    font-size: 16px;
  }

  .task-header__right {
    span {
      font-size: 16px;
    }
  }
}

.isCrowd {
  width: 700px !important;
}

:deep(.jb-mobile-time-picker__group__list-item) {
  background-color: #F4F4F4 !important;
}

.task-middle {
  padding-bottom: 10px !important;
}

.tab {
  margin-top: 16px !important;
  margin-bottom: 0 !important;
}

.task-situation__filter {
  margin-bottom: 5px;
}
</style>

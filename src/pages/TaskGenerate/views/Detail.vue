<template>
  <div class="detail">
    <div class="detail-header">
      <div class="detail-board">
        <img
          src="@/assets/images/common/task-book.png"
          class="detail-board__icon"
        />
        <img
          src="@/assets/images/common/task-msg-bg.png"
          class="detail-board__bg"
        />
        <div class="detail-board__content">
          <SvgIcon name="task-msg" size="20" />
          <div class="board">
            <span>{{ detailData.content }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-content">
      <div class="detail-content__title">当前状态：</div>
      <div class="detail-content__status">
        <div class="status-text">{{ detailData.statusName }}</div>
        <div class="status-container">
          <div
            class="status-stage status-stage__one"
            :class="detailData.status >= 1? 'active' : ''"
          >
            <SvgIcon
              name="task-status-1"
              :color="1 ? '#FFFFFF' : '#EFEFEF'"
              size="16"
            />
          </div>
          <div
            class="status-stage status-stage__two"
            :class="detailData.status >= 2 ? 'active' : ''"
          >
            <SvgIcon
              name="task-status-2"
              :color="1 ? '#FFFFFF' : '#EFEFEF'"
              size="16"
            />
          </div>
          <div
            class="status-stage status-stage__three"
            :class="detailData.status >= 3 ? 'active' : ''"
          >
            <SvgIcon
              name="task-status-3"
              :color="1 ? '#FFFFFF' : '#EFEFEF'"
              size="16"
            />
          </div>
          <div class="status-rectangle"></div>
          <div
            class="status-round"
            :style="detailData.status === 3 ? 'width:80%' : 'width:60%'"
          ></div>
        </div>
      </div>
      <div class="detail-content__progress">
        <div class="progress-wrap">
          <div
            class="progress-item"
            v-for="(item, index) in detailList"
            :key="index"
          >
            <div
              :class="{
                'progress-item__icon--active': true,
                'progress-item__icon': true,
              }"
            ></div>
            <div class="progress-content">
              <div class="progress-text">
                {{ item.operateContent }}
              </div>
              <div class="progress-label">
                <span v-if="operateContent && operateContent.length > 0 && item.operateType == 'INHAND'">{{
                  dayjs(operateContent[operateContent.length-1].operateTime).format("YYYY-MM-DD HH:mm")
                }}</span>
                <span v-else>{{
                  dayjs(item.operateTime).format("YYYY-MM-DD HH:mm")
                }}</span>
                <span v-if="item.operateType != 'INHAND'">操作人：{{item.operatorName}}</span>
                <span v-if="operateContent && operateContent.length > 0 && item.operateType == 'INHAND'" >操作人：{{operateContent[operateContent.length-1].operatorName}}</span>
              </div>
              <div class="progress-tip" v-if="index == 1">
                <span class="progress-tip__text" :title="operateContent[operateContent.length-1].operateContent" v-if="operateContent && operateContent.length > 0"
                  > {{operateContent[operateContent.length-1].operateContent}}</span
                >
                <span class="progress-tip__text"  v-else>备注</span>
                <SvgIcon name="task-edit" v-if="showBottom && (notesStatus !== 3 && !isCustomer)"
                         color="#ff6824"
                         size="16" @click="isRemark(item,
                index)" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-footer" v-if="showBottom" >
      <jb-mobile-button round plain type="primary" @click.stop="handleCopy"
        >复制当前页面链接</jb-mobile-button
      >
      <jb-mobile-button round type="primary" @click="transport">转发给客户</jb-mobile-button>
    </div>
  </div>
  <!-- 栏目输入选择 -->
  <remark-input
    v-model:visible="remarkInputVisible"
    @confirm="handleRemarkConfirm"
    :initial-text="initialRemarkText"
  />
</template>

<script setup lang="ts">
// 定义组件名称，用于keep-alive
defineOptions({
  name: 'Detail'
})

import dayjs from "dayjs";
// import ClipboardJS from "clipboard";
import { JbMobileButton } from "jb-mobile-ui";
import RemarkInput from "../components/RemarkInput.vue";
import { showToast } from "vant";
import {ref, onActivated, onMounted, computed} from "vue";
import { useRoute } from "vue-router";
import { detail, reply } from "@/api/workOrder";
import { useUserStore } from '@/stores/user'
import {initWWConfig} from "@/utils/handleInitWecom.ts";
import { getUrlState, copyText } from "@/utils/common.ts";
interface IDetailData {
  content?: string;
  statusName?: string;
  categoryName?: string;
  workOrderId?: string | number;
}
interface IDetailListItem {
  operateContent?: string;
  operateTime: string;
  operatorName: string;
}
// 显示操作按钮，只有群聊或者是通过群工单推送的🔗才能进来·
const showBottom = computed(() => {
  const {isCrowd} = route.query || {}
  console.log('isCrowd', isCrowd)
  console.log('isCustomer.value', isCustomer.value)
  console.log('route.query.isCustom', route.query.isCustom)
  
  // if (isCrowd && typeof isCrowd === "string" && !parseInt( isCrowd )) {
  //   return false
  // }
  if (isCustomer.value) {
    return false
  }
  // if (!isCustomer.value && !route.query.isCustom) {
  //   return true
  // }
  return true
  // return !(!isCustomer.value && route.query.isCrowd === '1');
})
// const hostname = import.meta.env.VITE_APP_REDIRECT_URI
const isCustomer = ref(false) // 判断是否为客户进来
const {userInfo} = useUserStore()
const detailId = ref('') // 工单id
const route = useRoute();
const detailData = ref<IDetailData>({});
const notesStatus = ref(-1)
const detailList = ref<IDetailListItem[]>([])
const operateContent = ref<IDetailListItem[]>([])
// 转发给客户
const transport = () => {
  initWWConfig(async (ww: any) => {
    console.log('detailId.value', detailId.value)
    console.log('ww', ww);
    if (!ww) {
      return 
    }
    // console.log('initWWConfig', ww)
    // console.log('detailData.value', detailData.value)
    const { content, categoryName, statusName } = detailData.value || {}
    const desc = (categoryName || '') + '|' + (statusName || '');
    const { VITE_APP_REDIRECT_URI } = import.meta.env || {}
    const link = `${VITE_APP_REDIRECT_URI}/TaskGenerate/detail?id=${detailId.value}&isCustom=1`
    console.log('link', link)
    await ww.shareAppMessage({
      title: content || '',
      desc,
      link ,
      imgUrl: 'https://staticcdn.jinbizhihui.com/images/group.png'
    })
  }, route)
}
const fetchDetail = async () => {
  try {
    const stateObj= getUrlState(route)
    // @ts-ignore
    detailId.value = stateObj?.id || route.query.state || route.query.id || 1

    const res = await detail(stateObj?.id || route.query.state || route.query.id || 1);
    detailList.value = []
    operateContent.value = []
    detailData.value = { ...res.data.data };
    const validOperateTypes = ['CREATE', 'INHAND', 'FINISH'] as const;
    detailData.value.details.forEach(element => {
      if (validOperateTypes.includes(element.operateType)) {
        detailList.value.push(element);
      }else{
        operateContent.value.push(element);
      }
    });
    notesStatus.value = res.data.data.status
  } catch (error) {
    console.error("获取详情失败:", error);
  }
};
const postReply = async (remarkText: string) => {
  try {
    const WorkOrderReplyDto = ref({
      workOrderId: detailData.value?.workOrderId,
      replyContent: remarkText,
      userId: userInfo.userId,
      userName: userInfo.userName,
    });
    await reply(WorkOrderReplyDto.value);
    await fetchDetail()
  } catch (error) {
    console.error("修改失败:", error);
  }
};
const operateLogList = ref([
  {
    remark: "1232131231",
    createTime: "2025-06-11",
  },
  {
    remark: "1232131231",
    createTime: "2025-06-11",
  },
]);
const remarkInputVisible = ref(false);
const initialRemarkText = ref("");
const isRemark = (item, index) => {
  remarkInputVisible.value = true;
  initialRemarkText.value = operateContent.value[operateContent.value.length - 1].operateContent || '';

};
// 使用通用复制函数处理复制链接
const handleCopy = async () => {
  try {
    // const { id, isCrowd } = detailObj || route.query  || {}
    // const config = await getConfig();
    const { VITE_APP_REDIRECT_URI } = import.meta.env || {}
    // const state = encodeURIComponent(JSON.stringify({
    //   id: detailId.value,
    //   isCustom: '1'
    // }))
    const href = `${VITE_APP_REDIRECT_URI}/TaskGenerate/detail?id=${detailId.value}&isCustom=1`
    // console.log('handleCopy state', decodeURIComponent(state))
    // 使用从 common.ts 导入的高兼容性复制函数
    console.log('href', href);

    const result = await copyText(href)

    if (result.success) {
      showToast("链接复制成功")
      console.log(`复制成功，使用方法: ${result.method}`)
    } else {
      // 根据不同的失败原因给出不同的提示
      if (result.method === 'manual-select') {
        showToast("请长按选择文本框中的链接进行复制")
      } else {
        showToast(result.error || "复制失败，请手动保存链接")
      }
      console.warn('复制失败:', result)
    }
  } catch (error) {
    console.error('复制操作异常:', error)
    showToast("复制失败，请重试")
  }
};
const handleRemarkConfirm = (remarkText: string) => {
  postReply(remarkText);
};
onMounted(() => {
  const { id, isCustom } = route.query || {}
  detailId.value = (Array.isArray(id) ? id[0] : id) || ''
  const customValue = Array.isArray(isCustom) ? isCustom[0] : isCustom
  isCustomer.value = customValue ? !!parseInt(customValue) : false
})
onActivated(() => {
  fetchDetail();
});
</script>

<style lang="less" scoped>
.detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  background-image: linear-gradient(180deg, rgba(255, 131, 25, 0.8) 0%, rgba(255, 202, 98, 0.8) 20%);
  box-sizing: border-box;
  padding-bottom: 88px;
  &-header {
    padding: 15px 10px 0 10px;
  }
  &-board {
    padding-top: 22px;
    position: relative;
    &__content {
      height: calc(100% - 22px);
      padding: 0 6px 6px 6px;
      border: 2px solid #ffffff;
      border-radius: 12px;
      position: relative;
      background-color: #ffde90;
      .svg-icon-task-msg {
        position: absolute;
        top: 18px;
        left: 24px;
        z-index: 3;
      }
      .board {
        // margin: 22px 7px 7px 7px;
        background: #ffffff;
        border-radius: 8px;
        padding: 16px 46px 16px 44px;
        position: relative;
        z-index: 2;
        font-weight: 600;
        font-size: 16px;
        color: #2b2f33;
        letter-spacing: 0.3px;
        line-height: 24px;
        min-height: 24px;
        word-wrap: break-word;
        word-break: break-word;
        overflow-wrap: break-word;
      }
    }
    &__icon {
      width: 45px;
      height: 45px;
      top: -7px;
      left: 8px;
      position: absolute;
      z-index: 3;
    }
    &__bg {
      width: 100%;
      height: 64px;
      top: 0;
      left: 0;
      position: absolute;
      z-index: 1;
    }
  }
  &-content {
    padding: 16px 16px 88px;
    position: relative;
    background-color: #fff;
    border-radius: 20px 20px 0 0;
    flex: 1;
    z-index: 1;
    margin-bottom: -88px;
    // &::before {
    //   content: "";
    //   position: absolute;
    //   background-color: #fff;
    //   width: 100%;
    //   height: 16px;
    //   top: -16px;
    //   left: 0;
    //   border-radius: 20px 20px 0 0;
    // }
    &__title {
      font-weight: 500;
      font-size: 14px;
      color: #666666;
      padding-left: 10px;
      position: relative;
      &::before {
        content: "";
        position: absolute;
        background-color: #fff;
        top: 2px;
        left: 0;
        border-radius: 20px 20px 0 0;
        width: 4px;
        height: 16px;
        background: #ff6a2b;
        border-radius: 100px 0 100px 0;
      }
    }
    &__status {
      .status {
        &-text {
          font-weight: 600;
          font-size: 20px;
          color: #ff6824;
          text-align: center;
          margin: 20px 0 12px;
        }
        &-container {
          width: 232px;
          height: 28px;
          position: relative;
          margin: 0 auto;
        }
        &-rectangle,
        &-round {
          top: 8px;
          position: absolute;
          width: 180px;
          height: 12px;
          background-color: #efefef;
          left: 26px;
          z-index: 1;
        }
        &-round {
          z-index: 2;
          width: 140px;
          background: #ff6a2b;
          border-radius: 0 100px 100px 0;
          &.active {
            width: 180px;
          }
        }
        &-stage {
          width: 28px;
          height: 28px;
          border-radius: 50%;
          background-color: #efefef;
          position: absolute;
          top: 0;
          z-index: 3;
          display: flex;
          justify-content: center;
          align-items: center;
          &__two {
            left: 102px;
          }
          &__three {
            right: 0;
          }
          &.active {
            background: #ff6a2b;
          }
        }
      }
    }
    &__progress {
      border: 1px solid #ffe7dc;
      border-radius: 12px;
      margin-top: 24px;
      .progress {
        &-wrap {
          margin-top: 16px;
          padding: 0 16px 12px 16px;
        }
        &-item {
          padding: 0 0 24px 13.5px;
          position: relative;
          margin-left: 5.5px;
          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 18px;
            border-left: 1px dashed #ff6824;
            height: calc(100% - 16px);
            z-index: 1;
          }
          &:last-child {
            padding-bottom: 0;
            &::before {
              display: none;
            }
          }
          &__icon {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #fff;
            border: 1px solid #d4d5d5;
            left: 0;
            top: 4px;
            z-index: 2;
            transform: translateX(calc(-50% + 0.5px));
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            box-sizing: border-box;
            &--active {
              border-color: #ff6824;
              &::after {
                position: absolute;
                border-radius: 50%;
                content: "";
                width: 6px;
                height: 6px;
                background-color: #ff6824;
                border-radius: 50%;
                transform: translateX(-50%);
                left: 50%;
              }
            }
          }
          &__svg {
            font-size: 12px;
            &--active {
              color: #ff6824;
            }
          }
        }
        &-text {
          line-height: 20px;
          font-weight: 500;
          font-size: 14px;
          color: #2b2f33;
          letter-spacing: 0;
          margin-bottom: 2px;
          .staff {
            color: #ff6824;
          }
        }
        &-label {
          line-height: 17px;
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          display: flex;
          justify-content: space-between;
        }
        &-tip {
          display: flex;
          align-items: center;
          &__text {
            font-weight: 400;
            font-size: 12px;
            color: #ff6824;
            line-height: 17px;
          }
          .svg-icon-task-edit {
            margin-left: 4px;
          }
        }
      }
    }
  }
  &-footer {
    width: 100vw;
    display: flex;
    justify-content: space-between;
    height: 88px;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 13px 16px 31px;
    z-index: 2;
    :deep(.van-button) {
      width: 166px;
      box-sizing: border-box;
    }
  }
}
.progress-tip__text{
    max-width: 250px;
    overflow: hidden;
    // white-space: nowrap;
    text-overflow: ellipsis;
}
.detail-board__content {
  margin-bottom: 16px;
}
</style>

import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuard } from '@/hooks/useRouterGuard'

const router = createRouter({
  history: createWebHistory('/TaskGenerate'),
  routes: [
    {
      path: '/',
      redirect: '/index'
    },
    {
      name:'index',
      path: '/index',
      component: () => import('../views/Index.vue'),
    },
    {
      name:'detail',
      path: '/detail',
      component: () => import('../views/Detail.vue'),
      meta: {
        keepAlive: true
      }
    },
    {
      name:'config',
      path: '/config',
      component: () => import('../views/Config.vue'),
      meta: {
        keepAlive: true
      }
    },
  ],
})
// 使用公共路由守卫
setupRouterGuard(router);
export default router

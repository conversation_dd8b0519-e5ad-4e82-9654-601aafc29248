<template>
  <jb-mobile-popup
    v-model:visible="visible"
    v-bind="attrs"
    :title="title"
    :grey-bg="true"
    :foot-button="false"
    style="height: 589px;overflow-y: hidden;"
    @close="handleClose"
  >
<!--    <slot name="header">-->
<!--    </slot>-->
    <div class="project">
      <div class="project-search">
        <jb-mobile-search-input
          :placeholder="placeholder"
          :show-btn="false"
          v-model:modelValue="modelValue"
        ></jb-mobile-search-input>
      </div>
      <div class="project-content">
        <div class="project-content__wrap">
          <div @click="getCallUp(item)" :class="{ active: (orgId === item[keyName]) || (orgId === item.orgId) || (orgId === item.houseId) }"
               class="item"
               v-for="item in renderList" :key="item[keyName] || item.orgId || item.houseId" ref="orgRef">
            <span class="item-text ellipsis"
              >{{ props.isNeedWechatGroupNameGroups ? item.wechatGroupName : item.houseName}}</span>
            <SvgIcon v-if="(orgId === item[keyName]) || (orgId === item.orgId) || (orgId === item.houseId)"
                     name="task-selected" />
          </div>

        </div>
      </div>
      <div class="project-footer" v-if="false">
        <jb-mobile-button round plain type="primary" @click="visible=false">取消</jb-mobile-button>
        <jb-mobile-button round type="primary" @click="getSure">确认</jb-mobile-button>
      </div>
    </div>
  </jb-mobile-popup>
</template>

<script setup lang="ts">
import {
  JbMobilePopup,
  JbMobileSearchInput,
  JbMobileButton,
} from 'jb-mobile-ui'
import { computed, onMounted, ref, useAttrs, watch, nextTick } from 'vue'
import { debounce, has} from "lodash-es";
const attrs = useAttrs()
const emits = defineEmits(['update:visible','update:placeholder','update:title', 'confirm',
  'close','update:customizeData','update:data', 'update:orgId'])
interface ISelectedItem {
  [key: string]: string | number;
  orgId: string | number;
  houseId: string | number;
  // wechatGroupName: string;
  // houseName: string;
}
const props = defineProps({
  // 当前组件是否默认选中某个值， true则是需要默认选中，false则相反
  hasSelected: {
    type: Boolean,
    default: true,
  },
  keyName: {
    type: [String, Number],
    default: ''
  },
  visible: {
    type: Boolean,
    default: false,
  },
  customizeData:{
    type:Array,
    default:[]
  },
  placeholder:{
    type:String,
    default:'搜索项目名称'
  },
  title:{
    type:String,
    default:'选择项目'
  },
  orgId: {
    type: [Number, String],
    default: 0,
  },
  isNeedWechatGroupNameGroups: {
    type: Boolean,
    default: false,
  }
})
const title = computed(() => props.title)
const hasSelected = computed(() => props.hasSelected) // 当前组件是否默认选中某个值
const keyName = computed(() => props.keyName)
const orgRef = ref()
const orgId = computed(() => props.orgId)
const renderList = ref<any[]>([])
const customizeData = computed(() => props.customizeData)
watch(() => customizeData.value, (val) => {
  renderList.value = val;
})
// 显示和隐藏弹出层
const visible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
    // selectedIndex.value = null
  },
})

// const selectedIndex = ref<number | null>(null);
const selectedItem = ref({})
const modelValue = ref('')
const getCallUp = (item: any) => {
  selectedItem.value = item
  console.log('keyName.value', keyName.value)
  emits('update:orgId', keyName.value ? item[keyName.value] : (item.orgId || item.houseId))
  // selectedIndex.value = index;
  // selectedItem.value = item
  nextTick(() => {
    getSure()
  })
}
watch(modelValue, debounce((val) => {
  console.log('val', val)

  if (customizeData.value?.length && val) {
    renderList.value = customizeData.value.filter(item => item.houseName.includes(val))
    console.log('filterList', renderList.value)
  } else {
    renderList.value = customizeData.value;
  }
}, 300))

const getSure = () =>{
  visible.value = false;
  emits('update:data', selectedItem.value)
}

// 滚动到指定orgId的项
const scrollToOrgId = (targetOrgId: number | string) => {
  if (!renderList.value || !orgRef.value) return;

  // 找到匹配的项的索引 - 使用与模板相同的匹配逻辑
  const targetIndex = renderList.value.findIndex((item: any) => {
    return (targetOrgId === item[keyName.value]) ||
           (targetOrgId === item.orgId) ||
           (targetOrgId === item.houseId);
  });

  if (targetIndex !== -1 && orgRef.value[targetIndex]) {
    console.log('找到选中项，索引:', targetIndex, '滚动到该位置')
    // 使用nextTick确保DOM已更新
    nextTick(() => {
      orgRef.value[targetIndex]?.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    });
  } else {
    console.log('未找到匹配的选中项，targetOrgId:', targetOrgId)
  }
}
const handleClose = () => {
  if (props.keyName === 'wechatGroupId' && props.orgId) {
    emits('update:data', selectedItem.value)
  }
}

// 监听弹窗显示状态，当弹窗打开且有orgId时滚动到对应项
watch(visible, (newVisible) => {
  if (newVisible && orgId.value) {
    // 延迟执行，确保DOM渲染完成
    setTimeout(() => {
      scrollToOrgId(orgId.value);
    }, 100);
  }
});


// 新增：默认选中第一项的逻辑
watch([visible, customizeData], ([isVisible, data]) => {
  if (!hasSelected.value) {
    return
  }
  // 不默认选中第一项
  // if (isVisible && data?.length > 0) {
  //   // 如果没有选中的项，则默认选中第一项
  //   const hasSelected = data.some((item: any) =>
  //     orgId.value === item[keyName.value] ||
  //     orgId.value === item.orgId ||
  //     orgId.value === item.houseId
  //   )
  //   if (!hasSelected) {
  //     const firstItem = data[0] as ISelectedItem
  //     selectedItem.value = firstItem;
  //     emits('update:orgId', keyName.value ? firstItem?.[keyName.value] : (firstItem?.orgId || firstItem?.houseId))
  //   }
  // }
}, { immediate: true })
</script>

<style lang="less" scoped>
:deep(.jb-mobile-popup__content) {
  height: 589px !important;
  overflow-y: hidden !important;
}
:deep(.project-search) {
  padding: 0 !important;
  padding-bottom: 10px !important;
  padding-top: 0 !important;
}
.project-content__wrap {
  div:first-child {
    margin-top: 0 !important;
  }
}
.project {
  height: 534px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  //padding-bottom: 88px;
  &-search {
    // position: absolute;
    // left: 0;
    // top: 0;
    // width: 100%;
    padding: 0 10px 16px 10px !important;
    box-sizing: border-box;
    :deep(.jb-mobile-search-input) {
      height: 35px;
      background-color: #ffffff;
    }
    :deep(.jb-mobile-search-input__inner) {
      background-color: #fff;
    }
    :deep(.jb-mobile-button) {
      height: 31px;
      margin-top: 2px;
    }
  }
  &-content {
    flex: 1;
    overflow: auto;
    padding: 0 10px;
    height: 800px;
    .item {
      box-sizing: border-box;
      height: 52px;
      line-height: 50px;
      background: #ffffff;
      border-radius: 12px;
      margin-top: 10px;
      padding: 0 16px;
      font-weight: 400;
      font-size: 14px;
      color: #2b2f33;
      border: 1px solid #ffffff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      :first-child{
        margin-top: 0 !important;
      }
      &-text {
        width: 289px;
      }
      .svg-icon-task-selected {
        font-size: 24px;
      }
      &.active {
        border-color: #ff6824;
        font-weight: 500;
        color: #ff6824;
      }
    }
  }
  &-footer {
    width: 100vw;
    display: flex;
    justify-content: space-between;
    height: 88px;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 13px 16px 31px;
    background-color: #fff;
    :deep(.van-button) {
      width: 166px;
      box-sizing: border-box;
    }
  }
}
.project-content{
  box-sizing: border-box;
  height: 100%;
  overflow-y: scroll;
  padding-bottom: 48px;
  //margin-bottom: 30px;
}
.project-footer{
  height: 100px;
}
</style>

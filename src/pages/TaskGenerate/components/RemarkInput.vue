<template>
  <jb-mobile-popup
    v-model:visible="visible"
    v-bind="attrs"
    title="填写备注"
    :grey-bg="false"
    :foot-button="false"
    :content-height="534"
  >
    <div class="remark">
      <van-field
        class="remark-textarea"
        v-model="submitRemark"
        rows="5"
        autosize
        type="textarea"
        placeholder="请输入"
        :maxlength="300"
        :show-word-limit="false"
        :disabled="false"
      />
      <div class="remark-footer">
        <jb-mobile-button @click="handleConfirm" :disabled="!submitRemark" round type="primary"
          >确认</jb-mobile-button
        >
      </div>
    </div>
  </jb-mobile-popup>
</template>

<script setup lang="ts">
import {
  JbMobilePopup,
  JbMobileSearchInput,
  JbMobileButton,
} from 'jb-mobile-ui'
import { computed, ref, useAttrs, watch } from 'vue'

const attrs = useAttrs()
const emits = defineEmits(['update:visible', 'confirm', 'close'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  initialText:{
    type:String,
    default:''
  }
})
const submitRemark = ref(props.initialText || ''); // 初始化赋值为父组件传入的值
const handleConfirm = () => {
  emits('confirm', submitRemark.value);
  emits('update:visible', false);
  submitRemark.value = ''
};
// 监听父组件传入的 initialText 变化（可选）
watch(() => props.initialText, (newVal) => {
  submitRemark.value = newVal;
});
// 显示和隐藏弹出层
const visible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  },
})
</script>
<style lang="less" scoped>
.remark {
  height: 451px;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  padding-bottom: 88px;
  &-textarea {
    height: 100%;
    background-color: #f4f4f4;
  }
  &-footer {
    width: 100vw;
    display: flex;
    height: 88px;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    bottom: 0;
    padding: 13px 16px 31px;
    background-color: #fff;
    :deep(.van-button) {
      width: 100%;
      box-sizing: border-box;
    }
  }
}
</style>

import { createApp } from 'vue'
import piniaEntity from '@/stores'

import App from './App.vue'
import router from './router'
import 'vant/lib/index.css'
import 'jb-mobile-ui/dist/index.css'
import '@/assets/main.less'
import 'virtual:svg-icons-register'
import './base.css'

import SvgIcon from '@/components/SvgIcon.vue'
import { vLoading } from '@/directives/index.ts'

const app = createApp(App)
app.component('SvgIcon', SvgIcon)
app.use(piniaEntity)
app.use(router)

app.directive('loading', vLoading)

app.mount('#app')

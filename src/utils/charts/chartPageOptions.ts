import * as echarts from "echarts/core";

// utils
import { customRound } from '@/utils/common.ts'

const themeColor = '#7b52fe'

const getLineChartSeries = (opts: Record<string, any>) => {
  return {
    name: opts.name,
    type: "line",
    data: opts.data,
    smooth: false,
    symbol: "circle",
    symbolSize: 5,
    lineStyle: {
      color: opts.color,
      width: 1,
    },
    itemStyle: {
      color: opts.color,
    },
    areaStyle: opts.hasArea ? {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: "rgba(123,82,254,0.15)" },
        { offset: 1, color: "rgba(123,82,254,0.03)" },
      ]),
    } : undefined,
  }
}

export const eventLineChartOptions = (eventLineData: any) => ({
  grid: { left: 30, right: 0, top: 30, bottom: 43 },
  tooltip: {
    trigger: "axis",
    backgroundColor: "#fff",
    borderColor: "#eee",
    borderWidth: 1,
    textStyle: { color: "#333", fontSize: 14 },
    formatter: eventLineData.tooltipFormatter || ((params: any) => {
      const p = params[0];
      return `<div style='padding:6px 12px;'>日期: ${p.name}<br/>${eventLineData.lineYAxisLabel}: ${p.value}</div>`;
    }),
    extraCssText: "box-shadow:0 2px 8px rgba(0,0,0,0.08);border-radius:8px;",
  },
  xAxis: {
    // type: "category",
    // boundaryGap: false,
    data: eventLineData.dates,
    axisLabel: { color: "#999", fontSize: 13, interval: 0, formatter: eventLineData.xAxisLabelFormatter || ((value: string) => value) },
    axisLine: { lineStyle: { color: "#E4E7ED" } },
    axisTick: { alignWithLabel: true },
  },
  yAxis: {
    type: "value",
    // min: 0,
    // max: 100,
    // interval: 20,
    name: eventLineData.lineYAxisLabel,
    nameLocation: "end",
    nameTextStyle: {
      color: "#999999",
      fontSize: 12,
      fontWeight: 500,
      lineHeight: 17,
      //   textAlign: "left",
      fontFamily: "PingFangSC, PingFang SC",
      padding: [0, 0, 0, -35], // 向左微调
      //   padding: [0, 0, 8, 0]
    },
    axisLabel: { color: "#999", fontSize: 13 },
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { lineStyle: { color: "#EBEEF5", type: "dashed" } },
  },
  series: eventLineData.values.map((item: Record<string, any>[], index: number) => {
    return getLineChartSeries({
      ...eventLineData,
      name: eventLineData.legendList?.[index] || eventLineData.lineYAxisLabel,
      data: item,
      color: eventLineData.colorList?.[index] || themeColor,
    })
  }),
  dataZoom: eventLineData.hasDataZoom && eventLineData.values[0].length > 8 ? [
    {
      type: 'inside',
      start: 0,
      end: 20,
      zoomOnMouseWheel: false,
      moveOnMouseWheel: true
    }
  ] : [],
  legend: {
    data: eventLineData.legendList || [eventLineData.lineYAxisLabel],
    itemWidth: 15,
    itemHeight: 5,
    bottom: -5
  },
  //   legend: {
  //     data: ["负面典型事件"],
  //     bottom: 0,
  //     left: "center",
  //     icon: "circle",
  //     itemWidth: 8,
  //     itemHeight: 8,
  //     textStyle: { color: "#7B52FE", fontSize: 13 },
  //   },
});
export const trendLineChartOptions = (opts: Record<string, any>) => ({
  grid: { left: 10, right: 10, top: 0, bottom: 43 },
  xAxis: {
    boundaryGap: false,
    data: opts.xAxisData,
    axisLabel: { color: '#999999', fontSize: 12, interval: 0 },
    axisLine: { show: true },
    // axisTick: { alignWithLabel: true },
    splitLine: {
      show: true,
      lineStyle: {
        type: 'dashed',
      },
    },
  },
  yAxis: {
    min: 0,
    max: 120,
    axisLabel: { show: false },
    splitLine: { lineStyle: { color: '#f2f2f2' } },
  },
  series: {
    name: opts.name,
    type: 'line',
    data: opts.seriesData,
    smooth: false,
    symbolSize: 6,
    lineStyle: {
      color: opts.color,
      width: 1,
    },
    itemStyle: {
      color: opts.color,
    },
    label: {
      show: true,
      color: opts.color,
    }
  },
  legend: {
    data: [opts.name],
    itemWidth: 15,
    itemHeight: 5,
    bottom: -5
  },
})
export const typePieChartOptions = (pieData: any, pieTitle: string = '', total: number = 0) => ({
  tooltip: {
    trigger: "item",
    backgroundColor: "#fff",
    borderColor: "#eee",
    borderWidth: 1,
    textStyle: { color: "#333", fontSize: 14 },
    formatter: (p: any) => `${p.name}<br/>数量：${p.value}`,
    extraCssText: "box-shadow:0 2px 8px rgba(0,0,0,0.08);border-radius:8px;",
  },
  series: [
    {
      type: "pie",
      radius: [48, 72],
      //   radius: "79%",
      center: ["50%", pieData.length > 2 ? 80 : 95],
      //   avoidLabelOverlap: true,
      label: {
        show: true,
        position: "outside",
        formatter: "{b}：{d}%",
        // color: "#86909C",
        // fontSize: 12,
        // fontWeight: 400,
        // alignTo: "labelLine",
        // overflow: "break",
        // ellipsis: "...",
      },
      labelLine: {
        show: true,
        // length: 10,
        // length2: 10,
        // minTurnAngle: 60,
        // smooth: false,
        // lineStyle: {
        //   color: (params: Record<string, any>) => {
        //     console.log("params", params);
        //     return params.color;
        //   },
        //   width: 1.5,
        // },
      },
      data: pieData,
      emphasis: {
        scale: true,
        itemStyle: {
          shadowBlur: 10,
          shadowColor: "rgba(123,82,254,0.15)",
        },
      },
      // 中心label用rich实现
      labelLayout: {
        hideOverlap: true,
      },
    },
  ],
  graphic: [
    {
      type: "text",
      left: "center",
      // bottom: '50%',
      top: pieData.length > 2 ? 75 : 90,
      z: 10,
      style: {
        text: pieTitle,
        fill: "#666",
        fontSize: 14,
        fontWeight: 600,
        fontFamily: "PingFangSC, PingFang SC",
        textAlign: "center",
        textVerticalAlign: "middle",
      },
    },
  ],
  legend: {
    show: false,
    bottom: 0, // 放在图表底部
    left: "center", // 居中
    orient: "horizontal",
    icon: "rect",
    itemWidth: 10,
    itemHeight: 10,
    textStyle: {
      color: "#999",
      fontSize: 13,
      fontWeight: 400,
      fontFamily: "PingFangSC, PingFang SC",
    },
    formatter: (name: string) => {
      const itemData = pieData.find((item: Record<string, any>) => item.name === name)
      return `${name}：${customRound(itemData.value / total * 100, 2)}%`
    }
  },
});
export const typeBarChartOptions = (barLineData: any) => ({
  grid: { left: 40, right: 0, top: 40, bottom: 40 },
  // tooltip: {
  //   trigger: "axis",
  //   backgroundColor: "#fff",
  //   borderColor: "#eee",
  //   borderWidth: 1,
  //   textStyle: { color: "#333", fontSize: 14 },
  //   formatter: (params: any) => {
  //     const p = params[0];
  //     return `<div style='padding:6px 12px;'>${
  //       barLineData.names[p.dataIndex]
  //     }<br/>数量：${p.value}个</div>`;
  //   },
  //   extraCssText: "box-shadow:0 2px 8px rgba(0,0,0,0.08);border-radius:8px;",
  // },
  xAxis: {
    type: "category",
    data: barLineData.names,
    axisLabel: { width: 60, color: "#999", fontSize: 12, interval: 0, overflow: 'truncate' },
    axisLine: { lineStyle: { color: "#E4E7ED" } },
    axisTick: { alignWithLabel: true },
  },
  yAxis: {
    type: "value",
    // min: 0,
    // max: 100,
    // interval: 20,
    name: "数量",
    nameLocation: "end",
    nameTextStyle: {
      color: "#999999",
      fontSize: 12,
      fontWeight: 500,
      lineHeight: 17,
      fontFamily: "PingFangSC, PingFang SC",
      padding: [0, 0, 0, -35], // 向左微调
    },
    axisLabel: { color: "#999", fontSize: 13 },
    axisLine: { show: false },
    axisTick: { show: false },
    splitLine: { lineStyle: { color: "#EBEEF5", type: "dashed" } },
  },
  series: [
    {
      name: "数量",
      type: "bar",
      data: barLineData.values,
      barWidth: 24,
      barCategoryGap: '54px',
      itemStyle: {
        borderRadius: [12, 12, 0, 0],
        color: (params: Record<string, any>) => {
          return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: params.dataIndex === 0 ? '#615dee' : '#c2c0ff' },
            { offset: 1, color: "rgba(154,150,248,0.3)" },
          ])
        },
      },
    },
  ],
  dataZoom: barLineData.values.length > 8 ? [
    {
      type: 'inside',
      start: 0,
      end: 8,
      zoomOnMouseWheel: false,
      moveOnMouseWheel: true
    }
  ] : [],
});
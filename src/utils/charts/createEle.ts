// 创建环形图的客诉评分元素
export const createLabelContainer = () => {
    // 创建标签元素
    const labelContainer = document.createElement('div');
    labelContainer.className = 'score-label-container';
    labelContainer.style.position = 'absolute';
    labelContainer.style.bottom = '14%';
    labelContainer.style.left = '50%';
    labelContainer.style.transform = 'translateX(-50%)';
    labelContainer.style.backgroundColor = '#8344ff';
    labelContainer.style.border = '3px solid #ffffff';
    labelContainer.style.borderRadius = '30px';
    labelContainer.style.padding = '6px 24px';
    labelContainer.style.color = '#FFFFFF';
    labelContainer.style.fontSize = '14px';
    labelContainer.style.fontFamily = 'PingFangSC, PingFang SC';
    labelContainer.style.fontWeight = '500';
    labelContainer.style.lineHeight = '20px';
    labelContainer.style.textAlign = 'center';
    labelContainer.style.fontStyle = 'normal';
    labelContainer.style.display = 'flex';
    labelContainer.style.alignItems = 'center';
    labelContainer.style.justifyContent = 'center';
    labelContainer.style.zIndex = '10';
    labelContainer.style.width = 'auto';
    labelContainer.style.minWidth = '90px';
    labelContainer.style.height = '27px';
    // labelContainer.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    labelContainer.innerText = '客诉评分';
    return labelContainer
}

// 创建当前全国排名
export const createCurrentNationalRank = (ranking: string = '5/8') => {
    // 创建当前全国排名元素
    const currentNationalRank = document.createElement('div');
    currentNationalRank.className = 'current-national-rank';
    currentNationalRank.style.position = 'absolute';
    currentNationalRank.style.bottom = '-16px';
    currentNationalRank.style.right = '50%';
    currentNationalRank.style.transform = 'translateX(50%)';
    currentNationalRank.style.display = 'flex';
    currentNationalRank.style.alignItems = 'center';
    // currentNationalRank.style.gap = '5px';
    
    // 创建第一个span - 当前全国排名
    const rankLabel = document.createElement('span');
    rankLabel.innerText = '当前全国排名';
    rankLabel.style.color = '#999999';
    rankLabel.style.fontSize = '14px';
    rankLabel.style.fontWeight = 'normal';
    rankLabel.style.fontFamily = 'PingFangSC-Regular, PingFang SC';
    rankLabel.style.whiteSpace = 'nowrap';
    // 创建第二个span - 排名值
    const rankValueSpan = document.createElement('span');
    rankValueSpan.innerText = ranking;
    rankValueSpan.style.color = '#2B2F33';
    rankValueSpan.style.fontSize = '14px';
    rankValueSpan.style.fontWeight = '500';
    rankValueSpan.style.fontFamily = 'PingFangSC-Medium, PingFang SC';
    rankValueSpan.style.whiteSpace = 'nowrap';
    rankValueSpan.style.marginLeft = '4px';
    // 将span添加到父元素中
    currentNationalRank.appendChild(rankLabel);
    currentNationalRank.appendChild(rankValueSpan);
    
    return currentNationalRank;
}
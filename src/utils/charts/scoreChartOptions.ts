interface OptionValue {
    max: number;
    outerRingWidth: string | number;
    shadowRingBg: string;
    mainRingWidth: string | number;
    currentValue: string | number;
}

const option = ({max, outerRingWidth, shadowRingBg, mainRingWidth, currentValue}: OptionValue) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0
    },
    series: [
      // 外层灰色环形
      {
        type: 'gauge',
        startAngle: 225,
        endAngle: -45,
        min: 0,
        max: max,
        radius: '92%',
        center: ['50%', '50%'],
        pointer: { show: false },
        progress: {
          show: true,
          width: outerRingWidth,
          overlap: false,
          roundCap: false,
          clip: false,
          itemStyle: {
            color: {
              type: 'linear',
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: '#FFFFFF' },    // 起始白色
                { offset: 0.4, color: '#F4F4F4' },      // 中间红色
                { offset: 1, color: '#FFFFFF' }     // 结束白色
              ]
            }
          }
        },
        axisLine: {
          lineStyle: {
            width: outerRingWidth,
            color: [[1, 'rgba(250, 250, 250, 0)']]
          }
        },
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        anchor: { show: false },
        title: { show: false },
        detail: { show: false },
        data: [{ value: max }],
        z: 1
      },
      // 主环形图 - 彩虹环
      {
        type: 'gauge',
        startAngle: 225,
        endAngle: -45,
        min: 0,
        max: max,
        radius: '83%',
        center: ['50%', '50%'],
        pointer: { show: false },
        progress: {
          show: true,
          width: mainRingWidth,
          overlap: false,
          roundCap: false,
          clip: false,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 1,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0.1, color: '#FF6A7A' },   // 左下红色
                { offset: 0.2, color: '#FFC522' }, // 下方黄色
                { offset: 0.3, color: '#FFD662' },  // 右侧黄色
                { offset: 0.7, color: '#A488FF' }, // 上方紫色
                { offset: 1, color: '#7B52FE' }     // 左上淡紫色
              ]
            }
          }
        },
        axisLine: {
          lineStyle: {
            width: mainRingWidth,
            color: [[1, shadowRingBg]]
          }
        },
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        anchor: { show: false },
        title: { show: false },
        detail: {
          valueAnimation: true,
          fontSize: 32,
          fontFamily: "D-DIN-PRO-Bold",
          fontWeight: 'bold',
          color: '#7B52FE',
          offsetCenter: [0, '0%'],
          formatter: () => currentValue
        },
        data: [{ value: 100 }],
        z: 2
      },
      // 内侧阴影环形
      {
        type: 'gauge',
        startAngle: 225,
        endAngle: -45,
        min: 0,
        max: max,
        radius: '62%',
        center: ['50%', '50%'],
        pointer: { show: false },
        progress: {
          show: true,
          width: outerRingWidth,
          overlap: false,
          roundCap: false,
          clip: false,
          itemStyle: {
            color: {
              type: 'linear',
            //   x: 0,
            //   y: 1,
              x2: 1,
              y2: 0,
              colorStops: [
                { offset: 0, color: '#FFFFFF' },    // 起始白色
                { offset: 0.4, color: '#F4F4F4' },      // 中间红色
                { offset: 1, color: '#FFFFFF' }     // 结束白色
              ]
            }
          }
        },
        axisLine: {
          lineStyle: {
            width: outerRingWidth,
            color: [[1, 'rgba(250, 250, 250, 0)']]
          }
        },
        splitLine: { show: false },
        axisTick: { show: false },
        axisLabel: { show: false },
        anchor: { show: false },
        title: { show: false },
        detail: { show: false },
        data: [{ value: max }],
        z: 1
      }
    ]
  };
};

export default option;
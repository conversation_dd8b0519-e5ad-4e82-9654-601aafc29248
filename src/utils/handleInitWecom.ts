import * as ww from '@wecom/jssdk'
import {getWecomTicket, getWecomAgentTicket} from "@/api/wecom.ts";
import {getConfig} from "@/utils/common.ts";
import {ssoLogin} from "@/api/login.ts";
import {type RouteLocation, useRoute, useRouter} from 'vue-router'
// import SHA1 from 'crypto-js/sha1'

export function initWWConfig(cb?: Function, route?: RouteLocation, secret?: string) {
    const { VITE_APP_CORP_ID } = import.meta.env || {}; // 获取企业id
    const ticket = localStorage.getItem('sdk_ticket')
    // const appId = localStorage.getItem('appId_hsp')
    if (ticket) {
        registerWW(VITE_APP_CORP_ID, ticket, true, cb)
    } else {
        handleGetJsapiTicket(route, secret).then(res => {
            registerWW(VITE_APP_CORP_ID as string, res as string, true, cb)
        })
    }
}
// export const initWechatConfig = (config) => {
//     ww.config({
//         debug: false,
//         appId: config.appId,
//         timestamp: Number(config.timestamp),
//         nonceStr: config.nonceStr,
//         signature: config.signature,
//         jsApiList: ['checkJsApi', 'openLocation', 'getLocation']
//     });
// }
export function handleGetJsapiTicket(route?: RouteLocation, secret?: string) {
    return new Promise(async (resolve, reject) => {
        const config = await getConfig() || {}
        const { VITE_APP_CORP_ID } = import.meta.env || {}; // 获取企业id
        // const se = localStorage.getItem('se')
        getWecomTicket({corpId: VITE_APP_CORP_ID, secret: secret || config.groupWorkOrderSecret}).then(async res => {
            const {code, data: ticket} = res?.data || {}
            if (code === '00000') {
                localStorage.setItem('sdk_ticket', ticket || '')
                resolve(ticket)
            } else {
                // const route = useRoute()
                // console.log('router', useRouter())
                console.log('route', route)
                const query = route?.query && Object.keys(route?.query)?.length ? encodeURIComponent(JSON.stringify(route?.query || {})) : ''
                const response = await ssoLogin(query);
                if (response.data.code === "00000") {
                    window.location.href = response.data.data;
                    return; // 跳转到SSO，不调用next()
                }
            }
        }).catch((err) => {
            reject(err)
        })
    })
}
export function handleGetAgentJsapiTicket(secret: string) {
    return new Promise(async (resolve, reject) => {
        const config = await getConfig() || {}
        const { VITE_APP_CORP_ID } = import.meta.env || {}; // 获取企业id
        getWecomAgentTicket({ corpId: VITE_APP_CORP_ID, secret: secret || config.groupWorkOrderSecret, url: location.href }).then(res => {
            const { code, data } = res?.data || {}
            if (code === '00000') {
                localStorage.setItem('sdk_agent_ticket', data.ticket || '')
                resolve(res?.data)
            }
        }).catch((err) => {
            reject(err)
        })
    })
}
// 企业
export function registerWW(corpid: string, jsapiTicket: string, firstTime = true, cb?: Function) {
    // const appTypeRes = localStorage.getItem('appType_hsp')!
    ww.register({
        corpId: corpid,       // 必填，当前用户企业所属企业ID
        // corpId: 'wxf8b68862a99c5a9c',       // 必填，当前用户企业所属企业ID
        jsApiList: ['checkJsApi', 'chooseImage', 'getLocalImgData', 'getNetworkType', 'onNetworkStatusChange', 'onHistoryBack', 'shareAppMessage', 'closeWindow'], // 必填，需要使用的JSAPI列表
        onConfigFail: function (err) {
            console.error('onConfigFail', err)
            if (firstTime) {
                handleGetJsapiTicket().then(res => {
                    registerWW(corpid, res as string, false)
                })
            }
        },
        getConfigSignature() {
            return ww.getSignature(jsapiTicket)
        },     // 必填，根据url生成企业签名的回调函数
    })
    if (cb && typeof cb === 'function') {
        cb(ww)
    }
}
// 应用
export function registerAgentWW(opts: Record<string, any>, flag = true) {
    opts = Object.assign({}, {
        agentId: '',
        secret: '',
        success: () => void 0,
    }, opts)
    return new Promise(async resolve => {
        const { VITE_APP_CORP_ID } = import.meta.env || {} // 获取企业的 id
        try {
            await handleGetAgentJsapiTicket(opts.secret).then((res: any) => {
                if (res.data && Object.keys(res.data)?.length) {
                    ww.register({
                        corpId: VITE_APP_CORP_ID,
                        agentId: opts.agentId,
                        jsApiList: ['getCurExternalChat', 'checkJsApi'],
                        getAgentConfigSignature (url) {
                            console.log('url', url)
                            console.log('res', res.data)
                            const { noncestr: nonceStr, signature, timestamp } = res.data || {}
                            return {
                                nonceStr, signature, timestamp
                            }
                        },
                        onConfigSuccess () {
                            console.log('onConfigSuccess')
                        },
                        onAgentConfigComplete () {
                            console.log('onAgentConfigComplete')
                        },
                        onAgentConfigSuccess () {
                            console.log('onAgentConfigSuccess')
                        },
                        onAgentConfigFail () {
                            console.log('onAgentConfigFail')
                            if (flag) {
                                registerAgentWW(opts, false)
                            }
                        }
                    })
                }
            })
        } catch (e) {}
        if (/wxwork/i.test(navigator.userAgent)) {
            requestAnimationFrame( () => {
                resolve(ww)
                if (opts.success && typeof opts.success === 'function') {
                    opts.success(ww)
                }
            })
        } else {
            resolve({})
            if (opts.success && typeof opts.success === 'function') {
                opts.success({})
            }
        }
    })
}

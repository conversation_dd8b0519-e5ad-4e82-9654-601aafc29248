// types
import type {TOrgData} from '@/types/common'
import {buildUUID} from 'jinbi-utils'
import * as ww from "@wecom/jssdk";
import SHA1 from 'crypto-js/sha1';
import { Watermark } from 'watermark-js-plus'
import {type RouteLocation} from 'vue-router'
/**
 * 扁平化 state 字段
 * @param state 需要扁平化的 state 字段
 * @param keyName 需要剥离的 key 名称
 * @returns 扁平化后的 state 字段
 */
function flattenStateRecursive(state: unknown, keyName: string = 'state') {
    let result: unknown = state;
    let lastResult: unknown;
    
    // 递归解码和解析
    while (typeof result === 'string') {
      try {
        lastResult = result;
        // 兼容多重拼接场景，清理所有?后的参数（如?wxkey=JBWY等）
        let cleanStr = result;
        // 处理可能的多重拼接，如 ...?wxkey=JBWY?foo=bar 等
        const questionIndex = cleanStr.indexOf('?');
        if (questionIndex !== -1) {
          cleanStr = cleanStr.substring(0, questionIndex);
        }
        result = decodeURIComponent(cleanStr);
        result = JSON.parse(result as string);
      } catch {
        result = lastResult;
        break;
      }
    }
    
    // 递归剥离 state 字段
    while (
      result &&
      typeof result === 'object' &&
      keyName in result &&
      (typeof (result as any)[keyName] === 'string' || typeof (result as any)[keyName] === 'object')
    ) {
      let inner = (result as any)[keyName];
      if (typeof inner === 'string') {
        try {
          // 兼容多重拼接场景，清理所有?后的参数
          let cleanStr = inner;
          const questionIndex = cleanStr.indexOf('?');
          if (questionIndex !== -1) {
            cleanStr = cleanStr.substring(0, questionIndex);
          }
          inner = decodeURIComponent(cleanStr);
          inner = JSON.parse(inner);
        } catch {
          break;
        }
      }
      result = inner;
    }
    return result;
  }
export const handleFlatStr = (keyName: string = 'state', value: unknown) => {
    // return `${key}=${value}`

      let flat = flattenStateRecursive(value, keyName);
      let flatStr = typeof flat === 'object' ? JSON.stringify(flat) : String(flat);
      return flatStr;
}
export const getConfig = async () => {
    const mode = import.meta.env.VITE_APP_CURRENT_MODE;
    try {
        if (mode === 'prod') {
            const config = await import('../../config/prod.config');
            return config.default;
        } else {
            const config = await import('../../config/test.config');
            return config.default;
        }
    } catch (error) {
        console.error('Failed to load config:', error);
        // 返回默认配置
        return {
            taskGenerateClientId: import.meta.env.VITE_APP_CLIENT_ID,
            taskGenerateSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
            complainAnalysisClientId: import.meta.env.VITE_APP_CLIENT_ID,
            complainAnalysisSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
            xuanlanMobileSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
            groupWorkOrderSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
        };
    }
};
const buildStr = (url: string) => {
    const noncestr = buildUUID()
    const timestamp = Math.floor(Date.now() / 1000)
    const jsapiTicket = localStorage.getItem('jsapiTicket') || ''
    const str = `jsapi_ticket=${jsapiTicket}&noncestr=${noncestr}&timestamp=${timestamp}&url=${url}`
    const signature = SHA1(str).toString()
    return {signature, noncestr, timestamp}
}
// const getConfigSignature= (url: string) => {
//   // console.log('getConfigSignature', url)
//   console.log('getConfigSignature', url)
//   return buildStr(url)
// }
// const getAgentConfigSignature = (url: string) => {
//   console.log('getAgentConfigSignature', url)
//   const obj = buildStr(url)
//   console.log('obj', obj)
//   return obj
// }
export const init = async ({jsapiTicket}: { jsapiTicket: string }) => {
    console.log('init', jsapiTicket)
    localStorage.setItem('jsapiTicket', jsapiTicket)
    const jsApiList = ['previewImage', 'previewFile', 'chooseImage', 'imagePreview', 'openDefaultBrowser', 'checkJsApi']
    const corpId = localStorage.getItem('appId') || import.meta.env.VITE_APP_CORP_ID
    console.log('corpId', corpId)
    ww.register({
        corpId,
        agentId: 1000152,
        jsApiList,
        getConfigSignature() {
            const signatureObj = ww.getSignature(jsapiTicket)
            const {timestamp, nonceStr, signature} = signatureObj || {}
            window.wx?.config({
                beta: true,// 必须这么写，否则wx.invoke调用形式的jsapi会有问题
                debug: false,
                appId: corpId,
                // appId: 'wxf8b68862a99c5a9c',
                nonceStr,
                timestamp,
                signature,
                jsApiList
            })
            return signatureObj
        },                // 必填，根据url生成企业签名的回调函数
        // getAgentConfigSignature,            // 必填，根据url生成应用签名的回调函数
        // getAgentConfigSignature,
        onConfigFail(err) {
            console.log('配置失败', err)
        },
        onConfigComplete(err) {
            console.log('err', err)
            ww.checkJsApi({
                jsApiList,
                success: (res) => {
                    console.log('checkJsApi success', res)
                },
                fail: (err) => {
                    console.log('checkJsApi fail', err)
                }
            })
            console.log('onConfigComplete', err)
        },
    })
}

// 将接口返回的数据解构出来
export const getDataFunc = (res: any) => {
    const {data} = res || {}
    const {code, data: subData} = data || {}
    if (code === '00000' || code === 1) { // 1 为 bi 接口所返回的 code
        return subData
    }
    return null
}

// 根据 orgId 查找组织树的节点信息
export const findOrgInfoByOrgId = (list: TOrgData[], orgId: number): TOrgData | null => {
    for (const item of list) {
        if (item.orgId === orgId) {
            return item
        }
        if (item?.child?.length) {
            const orgInfo = findOrgInfoByOrgId(item?.child, orgId)
            if (orgInfo) {
                return orgInfo
            }
        }
    }
    return null
}

// 递归处理当前组织 id，返回当前 id 以及子级的所有 id，返回的是一个数组
export const findOrgIdsByOrgId = (tree: TOrgData, id: number | string, key: string) => {
    // 结果数组，用于存储目标 orgId 和子级 orgId
    const result: number[] | string[] = []

    // 定义递归函数
    function findOrg(node: TOrgData) {
        if (!node) return
        // 如果找到目标 orgId，添加到结果中
        // @ts-ignore
        if (node[key] === id) {
            // @ts-ignore
            result.push(node[key])
            // 如果有子级，递归处理所有子级
            if (node.child) {
                node.child.forEach((childNode) => collectOrgIds(childNode))
            }
        } else if (node.child) {
            // 如果当前节点不是目标，但有子级，继续在子级中查找
            node.child.forEach((childNode) => findOrg(childNode))
        }
    }

    // 收集子级 orgId 的辅助函数
    function collectOrgIds(node: TOrgData) {
        if (!node) return
        // @ts-ignore
        result.push(node[key])
        if (node.child) {
            node.child.forEach((childNode) => collectOrgIds(childNode))
        }
    }

    // 开始递归查找
    // @ts-ignore
    findOrg(tree)
    return result
}

// 三舍四入
export const customRound = (num: number, decimalPlaces: number) => {
    const factor = 10 ** decimalPlaces
    const truncated = Math.trunc(num * factor) // 直接截断
    const remainder = (num * factor) - truncated // 小数部分
    return (remainder >= 0.4)
        ? (truncated + 1) / factor  // >= 0.4 进位
        : truncated / factor        // < 0.4 舍去
}

// 高兼容性复制文本函数 - 支持所有设备和浏览器环境
export const copyText = async (text: string): Promise<{ success: boolean; method: string; error?: string }> => {
    // 检测现代 Clipboard API 可用性
    const hasClipboardAPI = navigator.clipboard && typeof navigator.clipboard.writeText === 'function'
    // 检测是否为安全环境（HTTPS或localhost）
    const isSecureContext = window.isSecureContext || location.protocol === 'https:' || location.hostname === 'localhost'

    // 方法1: 优先使用现代 Clipboard API
    if (hasClipboardAPI && isSecureContext) {
        try {
            await navigator.clipboard.writeText(text)
            return { success: true, method: 'clipboard-api' }
        } catch (error) {
            console.warn('Clipboard API 失败，尝试降级方案:', error)
        }
    }

    // 方法2: 使用 document.execCommand (兼容性更好)
    if (document.execCommand) {
        try {
            const textarea = document.createElement('textarea')
            textarea.value = text
            textarea.style.position = 'fixed'
            textarea.style.top = '0'
            textarea.style.left = '0'
            textarea.style.width = '1px'
            textarea.style.height = '1px'
            textarea.style.padding = '0'
            textarea.style.border = 'none'
            textarea.style.outline = 'none'
            textarea.style.boxShadow = 'none'
            textarea.style.background = 'transparent'
            textarea.style.opacity = '0'
            textarea.style.zIndex = '-1000'

            document.body.appendChild(textarea)
            textarea.focus()
            textarea.select()
            textarea.setSelectionRange(0, text.length)

            const successful = document.execCommand('copy')
            document.body.removeChild(textarea)

            if (successful) {
                return { success: true, method: 'execCommand' }
            }
        } catch (error) {
            console.warn('execCommand 失败，尝试最终降级方案:', error)
        }
    }

    // 方法3: 最终兜底 - 创建可选择的文本区域
    try {
        const textarea = document.createElement('textarea')
        textarea.value = text
        textarea.style.position = 'fixed'
        textarea.style.top = '50%'
        textarea.style.left = '50%'
        textarea.style.transform = 'translate(-50%, -50%)'
        textarea.style.width = '80%'
        textarea.style.height = '100px'
        textarea.style.padding = '10px'
        textarea.style.border = '2px solid #ff6824'
        textarea.style.borderRadius = '8px'
        textarea.style.fontSize = '14px'
        textarea.style.zIndex = '10000'
        textarea.style.backgroundColor = '#fff'
        textarea.readOnly = true

        document.body.appendChild(textarea)
        textarea.focus()
        textarea.select()
        textarea.setSelectionRange(0, text.length)

        // 3秒后自动移除
        setTimeout(() => {
            if (document.body.contains(textarea)) {
                document.body.removeChild(textarea)
            }
        }, 3000)

        return {
            success: false,
            method: 'manual-select',
            error: '请手动复制上方文本框中的链接'
        }
    } catch (error) {
        return {
            success: false,
            method: 'failed',
            error: '复制功能不可用，请手动保存链接'
        }
    }
}

// 兼容性简化版本 - 保持向后兼容
export const copyTextSimple = (text: string): boolean => {
    const textArea = document.createElement('textarea')
    textArea.style.position = 'fixed'
    textArea.style.opacity = '0'
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()

    let isSuccess = false
    try {
        isSuccess = document.execCommand('copy')
    } catch (err) {
        isSuccess = false
    }

    document.body.removeChild(textArea)
    return isSuccess
}


// 根据搜索的组织架构名称筛选组织架构树
export const filterOrgList = (list: TOrgData[], value: string | number) => {
    console.log('filterOrgList', list, value);
    const valueStr = String(value).toLowerCase().replace(/[-~～\s]/g, '')

    // 1. 递归查找orgId精确等于value的节点
    const findNodeByOrgId = (nodes: TOrgData[]): TOrgData | null => {
        for (const node of nodes) {
            if (String(node.orgId) === valueStr) {
                return node
            }
            if (node.child && node.child.length) {
                const found = findNodeByOrgId(node.child)
                if (found) return found
            }
        }
        return null
    }

    const exactNode = findNodeByOrgId(list)
    if (exactNode) {
        return [exactNode]
    }

    // 2. 否则走原有模糊匹配
    const filteredList: TOrgData[] = []
    for (const item of list) {
        const hasChild = item.child && item.child.length
        const child = hasChild ? filterOrgList(item.child, value) : []
        const orgName = item.orgName.toLowerCase().replace(/[-~～\s]/g, '')
        const orgIdStr = String(item.orgId).toLowerCase()
        if (orgName.includes(valueStr) || orgIdStr.includes(valueStr) || child.length) {
            filteredList.push({...item, child})
        }
    }
    return filteredList
}
// 获取state，（针对iam跳转）
export const getUrlState = (route: RouteLocation) => {
    const state = route.query.state
    let stateObj: any = {}
    if (state && typeof state === 'string') {
        try {
            // 使用增强的递归扁平化处理，兼容多重编码和拼接
            const flatStr = handleFlatStr('state', state);
            stateObj = JSON.parse(flatStr);
        } catch (error) {
            // 降级到原有逻辑，保持兼容性
            if (state.indexOf('?') > -1) {
                const jsonPart = state.slice(0, state.indexOf('?'));
                try {
                    stateObj = JSON.parse(decodeURIComponent(jsonPart));
                } catch {
                    // 最终降级，返回空对象
                    stateObj = {};
                }
            } else {
                try {
                    stateObj = JSON.parse(decodeURIComponent(state));
                } catch {
                    // 最终降级，返回空对象
                    stateObj = {};
                }
            }
        }
    }    
    return stateObj
}
// 根据指定的 id 列表来过滤组织架构列表
export const filterTree = (nodes: Record<string, any>[], idList: number[], idKey: string = 'houseId'): Record<string, any>[] => {
    return nodes.filter(node => {
        // 先递归处理子节点
        const hasValidChildren = filterTree(node.child || [], idList, idKey).length > 0

        // 当前节点在指定的 id 列表中或者存在有效的子节点
        const flag = idList.includes(node[idKey]) || hasValidChildren

        if (flag) {
            // 更新子节点为过滤后的结果
            node.child = filterTree(node.child || [], idList, idKey)
        }
        return flag
    })
}
// 解析客诉的组织树
export const parseOrgList = (orgList: TOrgData[]): TOrgData[] => {
    return orgList?.map((item: TOrgData) => {
        return {
            ...item,
            orgId: item.houseId || item.organizationId,
            orgName: item.houseName || item.organizationName,
            child: parseOrgList(item?.child) || [],
        }
    })
}
// 生成水印
export const createWatermark = (content: string) => {
    const watermark = new Watermark({
        content,
        width: 100,
        height: 100,
        rotate: 22,
        layout: 'grid',
        fontSize: '14px',
        fontColor: '#d4d9e2',
        gridLayoutOptions: {
            rows: 2,
            cols: 2,
            gap: [20, 20],
            matrix: [[1, 0], [0, 1]]
        },
    })

    watermark.create()

    return watermark
}
// 生成随机 id
export const generateRandomId = () => {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

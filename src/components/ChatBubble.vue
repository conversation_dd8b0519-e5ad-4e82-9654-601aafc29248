<script setup lang="ts">
import { computed } from 'vue'
import dayjs from 'dayjs'
import { showImagePreview } from 'vant';
import userAvatarImg from '@/assets/images/png/user-avatar.png'
interface Props {
  // 消息基本信息
  content: string
  messageType: 'text' | 'image' | 'emotion' | 'video' | 'file' | 'link' | 'revoke' | string
  messageTime: string | number

  // 用户信息
  userName: string
  avatar: string
  isStaff: boolean // 是否是员工

  // 可选的额外信息
  fileName?: string
  linkUrl?: string
  linkTitle?: string
  messageTypeName?: string
}

const props = withDefaults(defineProps<Props>(), {
  messageType: 'text',
  fileName: '',
  linkUrl: '',
  linkTitle: '',
  messageTypeName: ''
})

// 计算属性
const isRevoke = computed(() => props.messageType === 'revoke')

const formattedTime = computed(() => {
  return dayjs(props.messageTime).format('HH:mm')
})

// 方法
const previewImage = () => {
  // 可以使用微信SDK或其他图片预览方案
  // console.log('预览图片:', props.content)
  showImagePreview({
    images: [props.content],
    startPosition: 0,
  })
  // 如果在微信环境中，可以调用微信SDK
  // ww.previewImage({ current: props.content, urls: [props.content] })
}
</script>
<template>
  <div class="chat-bubble-container" :class="{ 'right-align': isStaff, 'revoke-message': isRevoke }">
    <!-- 撤回消息 -->
    <template v-if="isRevoke">
      <div class="revoke-text">{{ userName }}撤回了一条消息</div>
    </template>

    <!-- 正常消息 -->
    <template v-else>
      <!-- 头像（非员工消息显示在左边，员工消息显示在右边） -->
      <div class="avatar-container" v-if="!isStaff">
        <van-image class="avatar" :src="avatar || userAvatarImg">
          <template v-slot:error>
            <img class="avatar" :src="userAvatarImg" alt="" />
          </template>
        </van-image>
      </div>
      <!-- 头像（员工消息显示在右边） -->
      <div class="avatar-container" v-if="isStaff">
        <van-image class="avatar" :src="avatar || userAvatarImg">
          <template v-slot:error>
            <img class="avatar" :src="userAvatarImg" alt="" />
          </template>
        </van-image>
      </div>
      <!-- 消息内容区域 -->
      <div class="message-container">
        <!-- 用户名和时间 -->
        <div class="message-header" :class="{ 'right-header': isStaff }">
          <span class="user-name">{{ userName }}</span>
          <span class="message-time">{{ formattedTime }}</span>
        </div>

        <!-- 消息气泡 -->
        <div class="message-bubble" :class="{ 'staff-bubble': isStaff, 'customer-bubble': !isStaff }">
          <!-- 文本消息 -->
          <div v-if="messageType === 'text'" class="text-content">
            {{ content }}
          </div>

          <!-- 图片/表情消息 -->
          <div v-else-if="['image', 'emotion'].includes(messageType)" class="image-content">
            <img class="message-image" :src="content" :alt="messageType" @click="previewImage" />
          </div>

          <!-- 视频消息 -->
          <div v-else-if="messageType === 'video'" class="video-content">
            <video class="message-video" controls :src="content">
              <div>您的浏览器不支持视频播放</div>
            </video>
          </div>

          <!-- 文件消息 -->
          <div v-else-if="messageType === 'file'" class="file-content">
            <a :href="content" target="_blank" class="file-link">
              📎 {{ fileName || '文件' }}
            </a>
          </div>

          <!-- 链接消息 -->
          <div v-else-if="messageType === 'link'" class="link-content">
            <a :href="linkUrl" target="_blank" class="message-link">
              🔗 {{ linkTitle || '链接' }}
            </a>
          </div>

          <!-- 其他类型消息 -->
          <div v-else class="other-content">
            【{{ messageTypeName || '未知消息' }}】
          </div>
        </div>

      </div>

    </template>
  </div>
</template>
<style scoped lang="less">
.chat-bubble-container {
  display: flex;
  //margin-bottom: 16px;
  align-items: flex-start;
  padding: 16px 9px;
  // 右对齐（员工消息）
  &.right-align {
    flex-direction: row-reverse;

    .message-container {
      align-items: flex-end;
    }

    .avatar-container {
      margin-left: 8px;
      margin-right: 0;
    }
  }

  // 撤回消息
  &.revoke-message {
    justify-content: center;
    margin: 8px 0;

    .revoke-text {
      color: #999;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.05);
      padding: 4px 12px;
      border-radius: 12px;
    }
  }
}

.avatar-container {
  margin-right: 8px;
  flex-shrink: 0;

  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
  }
}

.message-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: calc(100% - 60px);
  flex: 1;
}

.message-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: #666;

  &.right-header {
    flex-direction: row-reverse;

    .user-name {
      margin-left: 8px;
      margin-right: 0;
    }
  }

  .user-name {
    //font-weight: 500;
    margin-right: 8px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: #84888C;
    line-height: 17px;
    text-align: left;
    font-style: normal;
  }

  .message-time {
    color: #999;
  }
}

.message-bubble {
  position: relative;
  padding: 8px 12px;
  border-radius: 12px;
  max-width: 254px;
  box-sizing: border-box;
  word-wrap: break-word;
    // 客户消息气泡
  &.customer-bubble {
    background: #fff;
    //border: 1px solid #e5e5e5;

    // 左侧小三角 - 描边层
    &::after {
      content: '';
      position: absolute;
      left: -5px;
      top: 15px;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-right: 5px solid #e5e5e5;
      border-top: 5px solid transparent;
      border-bottom: 5px solid transparent;
      z-index: 10;
    }

    // 左侧小三角 - 白色填充层
    &::before {
      content: '';
      position: absolute;
      left: -4px;
      top: 15px;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-right: 6px solid #fff;
      border-top: 6px solid transparent;
      border-bottom: 8px solid transparent;
      z-index: 11;
    }
  }

  // 员工消息气泡
  &.staff-bubble {
    background: #C9E7FF;
    color: #2B2F33;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    // 右侧小三角
    &::before {
      content: '';
      position: absolute;
      right: -6px;
      top: 15px;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid #C9E7FF;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      z-index: 10;
    }
  }
}

// 不同类型消息的样式
.text-content {
  //line-height: 1.4;
  //font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #2B2F33;
}

.image-content {
  .message-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 8px;
    cursor: pointer;
    object-fit: cover;
  }
}

.video-content {
  .message-video {
    max-width: 200px;
    border-radius: 8px;
  }
}

.file-content, .link-content {
  .file-link, .message-link {
    color: #1890ff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}

.other-content {
  color: #666;
  font-style: italic;
}
</style>

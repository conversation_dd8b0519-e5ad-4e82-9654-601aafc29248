<script setup lang="ts">

</script>

<template>
  <div class="loading-mask">
    <div class="loading-spinner">
      <div class="loading-inner one"></div>
      <div class="loading-inner two"></div>
      <div class="loading-inner three"></div>
      <img class="loading-icon" src="@/assets/images/svg/loading-icon.svg" alt="" />
    </div>
  </div>
</template>

<style scoped lang="less">
.loading-mask {
  //background-color: rgba(255, 255, 255, .9);
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 2000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 65px;
  height: 65px;
  perspective: 780px;

  .loading-inner {
    position: absolute;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    border-radius: 50%;

    &.one {
      left: 0;
      top: 0;
      border-bottom: 3px solid #5c5edc;
      animation: rotate1 1.15s linear infinite;
    }

    &.two {
      right: 0;
      top: 0;
      border-right: 3px solid #9147ff;
      animation: rotate2 1.15s 0.1s linear infinite;
    }

    &.three {
      right: 0;
      bottom: 0;
      border-top: 3px solid #3b82f6;
      animation: rotate3 1.15s 0.15s linear infinite;
    }
  }

  .loading-icon {
    width: 24px;
    height: 24px;
  }
}

@keyframes rotate1 {
  0% {
    transform: rotateX(45deg) rotateY(-45deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(45deg) rotateY(-45deg) rotateZ(360deg);
  }
}
@keyframes rotate2 {
  0% {
    transform: rotateX(45deg) rotateY(45deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(45deg) rotateY(45deg) rotateZ(360deg);
  }
}
@keyframes rotate3 {
  0% {
    transform: rotateX(-60deg) rotateY(0deg) rotateZ(0deg);
  }
  100% {
    transform: rotateX(-60deg) rotateY(0deg) rotateZ(360deg);
  }
}
</style>

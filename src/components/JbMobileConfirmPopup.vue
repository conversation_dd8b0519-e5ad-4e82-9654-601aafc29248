<script setup lang="ts">
/**
 * 确认提示弹出层组件
 */
import { useAttrs, ref, computed, onMounted } from 'vue'

// components
import { JbMobileButton } from 'jb-mobile-ui'

const attrs = useAttrs()
const emits = defineEmits([
  /**
   * 更新弹出层的显示状态
   */
  'update:visible',
  /**
   * 确认按钮被点击时触发
   */
  'confirm',
  /**
   * 取消按钮被点击时触发
   */
  'cancel'
])
const props = defineProps({
  /**
   * 是否显示弹出层，可通过 v-model 进行双向数据绑定
   */
  visible: {
    type: Boolean,
    default: false
  },
  /**
   * 确认提示的内容
   */
  content: {
    type: String,
    default: ''
  },
  /**
   * 确认按钮的文本
   */
  confirmText: {
    type: String,
    default: '确认'
  },
  /**
   * 取消按钮的文本
   */
  cancelText: {
    type: String,
    default: '取消'
  },
})

const bodyDom = ref<HTMLElement>()

// 显示和隐藏弹出层
const visible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  }
})

onMounted(() => {
  bodyDom.value = document.body
})
</script>

<template>
  <van-popup
    class="jb-mobile-confirm-popup"
    round
    :teleport="bodyDom"
    v-bind="attrs"
    v-model:show="visible"
  >
    <div class="jb-mobile-confirm-popup__inner">
      <div class="jb-mobile-confirm-popup__content">{{ content }}</div>
      <div class="jb-mobile-confirm-popup__foot-button">
        <jb-mobile-button
          block
          round
          @click="emits('cancel')"
        >{{ cancelText }}</jb-mobile-button>
        <jb-mobile-button
          type="primary"
          block
          round
          @click="emits('confirm')"
        >{{ confirmText }}</jb-mobile-button>
      </div>
    </div>
  </van-popup>
</template>

<style scoped lang="less">
.jb-mobile-confirm-popup {
  &__inner {
    width: 316px;
    padding: 24px;
    box-sizing: border-box;
  }

  &__content {
    font-size: 16px;
    font-weight: 500;
    color: var(--base-font-color);
    text-align: center;
    line-height: 22px;
    margin-bottom: 24px;
  }

  &__foot-button {
    display: flex;
  }
}
</style>
<script setup lang="ts">
/**
 * 级联选择器组件，选项列表是从底部弹出
 */
import { useAttrs, useSlots, ref, computed, watch, nextTick } from 'vue'

// images
import checkboxIcon from '@/assets/images/svg/checkbox-icon.svg'
import checkboxIconSelected from '@/assets/images/svg/checkbox-icon-selected.svg'
import checkboxIconIndeterminate from '@/assets/images/svg/checkbox-icon-indeterminate.svg'

// components
import { JbMobilePopup, JbMobileEmpty, JbMobileButton } from 'jb-mobile-ui'

const attrs = useAttrs()
const slots = useSlots()
const emits = defineEmits([
  /**
   * 更新级联选择器所选择的值
   */
  'update:modelValue',
  /**
   * 点击确定按钮时触发，返回值为选中的最底层的选项所组成的列表
   */
  'confirm',
])
const props = defineProps({
  /**
   * 级联选择器的宽度
   */
  width: {
    type: [Number, String],
    default: 'auto'
  },
  /**
   * 级联选择器所选择的值，可通过 v-model 进行双向数据绑定
   */
  modelValue: {
    type: [Number, String, Array],
    default: ''
  },
  /**
   * 级联选择器未选择时的提示文案
   */
  placeholder: {
    type: String,
    default: '请选择'
  },
  /**
   * 选项列表弹出层的标题
   */
  popupTitle: {
    type: String,
    default: '请选择'
  },
  /**
   * 选项列表
   */
  options: {
    type: Array<Record<string, any>>,
    default: []
  },
  /**
   * 选项的自定义字段，可修改的字段有 label 和 value
   */
  fieldNames: {
    type: Object,
    default: () => ({})
  },
  /**
   * 是否禁用级联选择器
   */
  disabled: {
    type: Boolean,
    default: false
  },
})

const isShowPopup = ref(false) // 是否显示弹出层
const selectedList = ref<Record<string, any>[]>([]) // 当前选择的每一级选项所组成的列表
const activeTabIndex = ref(0) // 当前选中 tab 标签的索引
const selectedValueList = ref<Record<string, any>[]>([]) // 选中的最底层的选项所组成的列表

const computedWidth = computed(() => props.width === 'auto' ? props.width : `${props.width}px`)
const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
const fieldNames = computed(() => {
  return Object.assign({}, {
    label: 'label',
    value: 'value',
    child: 'child',
    checked: 'checked',
  }, props.fieldNames)
})
const options = computed(() => props.options)
// 当前层级的选项列表
const currentOptions = computed(() =>
  activeTabIndex.value > 0 ? selectedList.value[activeTabIndex.value - 1]?.[fieldNames.value.child] || [] : options.value
)
const selectedValueLabel = computed(() => selectedValueList.value.map(item => item.path.join('-')).join('、'))

watch(() => modelValue.value, (val) => {
  // 重置选中状态
  handleReset()
  // 弹出层显示时回显选中状态
  setDefaultSelectedValue(options.value, modelValue.value)
  // 选中子节点后递归修正所有父节点的选中状态（防止脏数据）
  updateAllParentCheckedStatus(options.value)
  selectedValueList.value = getCheckedBottomLevelItems(options.value)
})

// 显示弹出层
const showPopup = () => {
  if (props.disabled) return
  isShowPopup.value = true
}

// 点击选项
const handleClickOption = (item: Record<string, any>) => {
  // 替换当前层级及之后的选择
  selectedList.value = selectedList.value.slice(0, activeTabIndex.value)

  if (item[fieldNames.value.child]?.length > 0) {
    // 有下一级，则切换到下一个标签页
    selectedList.value.push(item)
    nextTick(() => {
      activeTabIndex.value++
    })
  }
}

// 选择选项
const handleSelectOption = (item: Record<string, any>) => {
  const checked = !item[fieldNames.value.checked]
  if (item[fieldNames.value.value] === -1) { // 全部
    handleToggleSelectAll(checked)
  } else {
    handleToggleSelectItem(item, checked)
    const checkAllItem = currentOptions.value.find((item: Record<string, any>) => item[fieldNames.value.value] === -1)
    checkAllItem.checked = currentOptions.value.filter((item: Record<string, any>) => item[fieldNames.value.value] !== -1).every((item: Record<string, any>) => item[fieldNames.value.checked])
  }
}

// 选中/取消选中当前层级的所有选项，如果选项有下一级，则也选中/取消选中下一级的所有选项
const handleToggleSelectAll = (checked: boolean) => {
  currentOptions.value.forEach((item: Record<string, any>) => {
    handleToggleSelectItem(item, checked)
  })
}

// 选中/取消选中当前选项，如果选项有下一级，则也选中/取消选中下一级的所有选项
const handleToggleSelectItem = (item: Record<string, any>, checked: boolean) => {
  item[fieldNames.value.checked] = checked
  if (item[fieldNames.value.child]?.length > 0) {
    item[fieldNames.value.child].forEach((childItem: Record<string, any>) => {
      handleToggleSelectItem(childItem, checked)
    })
  }
  // 递归更新所有父节点的 checked 状态
  updateAllParentCheckedStatus(options.value)
}

// 递归遍历整棵树，更新所有父节点的 checked 状态
const updateAllParentCheckedStatus = (options: Record<string, any>[]) => {
  options.forEach(option => {
    if (option[fieldNames.value.child]?.length > 0) {
      // 先递归处理子节点
      updateAllParentCheckedStatus(option[fieldNames.value.child])
      const children = option[fieldNames.value.child]
      // 如果所有子项都未选中，则父节点也应为未选中
      if (children.every((child: Record<string, any>) => !child[fieldNames.value.checked])) {
        option[fieldNames.value.checked] = false
      } else if (children.every((child: Record<string, any>) => child[fieldNames.value.checked])) {
        // 如果所有子项都选中，则父节点也选中
        option[fieldNames.value.checked] = true
      }
      // 同步“全部”选项的 checked
      const allItem = children.find((child: Record<string, any>) => child[fieldNames.value.value] === -1)
      if (allItem) {
        // 除“全部”外的子项
        const otherChildren = children.filter((child: Record<string, any>) => child[fieldNames.value.value] !== -1)
        allItem[fieldNames.value.checked] = !!(otherChildren.length > 0 && otherChildren.every((child: Record<string, any>) => child[fieldNames.value.checked]));
      }
    }
  })
}

// 判断选项是否被选中
const isOptionSelected = (option: Record<string, any>) => {
  // 如果存在子项，递归检查子项
  if (option[fieldNames.value.child]?.length > 0) {
    return option[fieldNames.value.child].some((child: Record<string, any>) =>
      isOptionSelected(child)
    )
  }
  // 如果选项自身被选中，直接返回 true
  if (option[fieldNames.value.checked]) {
    return true
  }
  // 没有子项且自身未选中，返回 false
  return false
}

// 判断选项是否部分选中
const isOptionIndeterminate = (option: Record<string, any>) => {
  const children = option[fieldNames.value.child]
  // 如果没有子项，直接返回 false
  if (!children || children.length === 0) {
    return false
  }

  let hasChecked = false
  let hasUnchecked = false

  for (const child of children) {
    const childChecked = child[fieldNames.value.checked]
    const childIndeterminate = isOptionIndeterminate(child)

    // 子项本身选中或部分选中，则视为有选中项
    if (childChecked || childIndeterminate) {
      hasChecked = true
    }
    // 子项本身未选中且子项的子项也没有选中或部分选中，则视为有未选中项
    if (!childChecked && !childIndeterminate && !isOptionSelected(child)) {
      hasUnchecked = true
    }

    // 同时存在选中和未选中项，直接返回部分选中状态
    if (hasChecked && hasUnchecked) {
      return true
    }
  }

  return hasChecked && hasUnchecked
}

// 获取选中的最底层的选项
const getCheckedBottomLevelItems = (options: Record<string, any>[]) => {
  const result: Record<string, any>[] = []
  options.forEach((option) => {
    if (option[fieldNames.value.child]?.length > 0) {
      // 存在子项，递归调用
      result.push(...getCheckedBottomLevelItems(option[fieldNames.value.child]))
    } else if (option[fieldNames.value.value] !== -1 && option[fieldNames.value.checked]) {
      // 没有子项且 checked 为 true，添加到结果数组
      result.push(option)
    }
  })
  return result
}

// 设置默认选中的值
const setDefaultSelectedValue = (options: Record<string, any>[], modelValue: any, parentChain: Record<string, any>[] = []) => {
  const valueArr = Array.isArray(modelValue) ? modelValue : [modelValue]
  options.forEach(option => {
    // 先递归处理子节点
    if (option[fieldNames.value.child]?.length > 0) {
      setDefaultSelectedValue(option[fieldNames.value.child], modelValue, [...parentChain, option])
    }
    // 如果当前节点被选中
    if (valueArr.includes(option[fieldNames.value.value])) {
      option[fieldNames.value.checked] = true
      // 父链全部 checked
      parentChain.forEach(parent => {
        parent[fieldNames.value.checked] = true
      })
    }
  })
}

// 清空选中的值
const clearSelectedValue = (options: Record<string, any>[]) => {
  options.forEach(option => {
    option[fieldNames.value.checked] = false
    if (option[fieldNames.value.child]?.length > 0) {
      clearSelectedValue(option[fieldNames.value.child])
    }
  })
}

// 确认
const handleConfirm = () => {
  isShowPopup.value = false
  selectedValueList.value = getCheckedBottomLevelItems(options.value)
  emits('confirm', selectedValueList.value)
}

// 重置
const handleReset = () => {
  selectedList.value = []
  clearSelectedValue(options.value)
}
</script>

<template>
  <div
    v-if="slots.reference"
    @click="showPopup"
  >
    <!-- @slot 自定义触发选项列表弹出层显示的 HTML 元素 -->
    <slot name="reference"></slot>
  </div>
  <div
    v-else
    class="jb-mobile-cascader"
    :class="{ disabled }"
    :style="{ width: computedWidth }"
    @click="showPopup"
  >
    <div class="jb-mobile-cascader__value ellipsis">{{ selectedValueLabel || placeholder }}</div>
    <img class="jb-mobile-cascader__select-down-icon" src="@/assets/images/common/select-down.png" alt="">
  </div>
  <jb-mobile-popup
    v-model:visible="isShowPopup"
    v-bind="attrs"
    :title="popupTitle"
  >
    <template v-if="options.length > 0">
      <van-tabs
        class="jb-mobile-cascader__tabs flex-column"
        animated
        swipeable
        v-model:active="activeTabIndex"
      >
        <van-tab
          v-for="(item, index) in selectedList.length + 1"
          :key="index"
        >
          <template #title>
            <div class="flex-align-center">
              <img v-if="index !== 0" class="right-arrow-icon common-icon" src="@/assets/images/svg/right-arrow-icon-gray-big.svg" alt="" />
              <div class="jb-mobile-cascader__tabs-title ellipsis">{{ selectedList[index]?.[fieldNames.label] || '请选择' }}</div>
            </div>
          </template>
          <div class="jb-mobile-cascader__tabs-content">
            <div
              v-for="item in currentOptions"
              :key="item[fieldNames.value]"
              class="jb-mobile-cascader__tabs-content__item flex-align-center"
              @click="handleClickOption(item)"
            >
              <div class="flex-align-center flex-1 ellipsis">
                <img
                  class="jb-mobile-cascader__checkbox"
                  :src="
                    isOptionIndeterminate(item)
                      ? checkboxIconIndeterminate
                      : isOptionSelected(item)
                      ? checkboxIconSelected
                      : checkboxIcon
                  "
                  alt=""
                  @click.stop="handleSelectOption(item)"
                />
                <div class="ellipsis">{{ item[fieldNames.label] }}</div>
              </div>
              <img v-if="item[fieldNames.child]?.length > 0" class="common-icon" src="@/assets/images/svg/right-arrow-icon-gray-big.svg" alt="" />
            </div>
          </div>
        </van-tab>
      </van-tabs>
    </template>
    <div v-else class="jb-mobile-cascader__empty-wrap">
      <jb-mobile-empty></jb-mobile-empty>
    </div>
    <template #footButton>
      <jb-mobile-button
        block
        round
        @click="handleReset"
      >重置</jb-mobile-button>
      <jb-mobile-button
        type="primary"
        block
        round
        @click="handleConfirm"
      >确定</jb-mobile-button>
    </template>
  </jb-mobile-popup>
</template>

<style scoped lang="less">
.jb-mobile-cascader {
  height: 36px;
  padding: 0 12px;
  background-color: #ffffff;
  border: 1px solid #e1e2e3;
  border-radius: 6px;
  box-sizing: border-box;
  display: flex;
  align-items: center;

  &.disabled {
    background-color: #eeeeee;
  }

  &__value {
    flex: 1 1 0;
    font-size: 14px;
    color: var(--base-font-color);
    white-space: nowrap;
    label-overflow: ellipsis;
    overflow: hidden;
    margin-right: 12px;
  }

  &__select-down-icon {
    width: 16px;
    height: 16px;
  }

  &__tabs {
    height: 396px;

    &-title {
      max-width: 46px;
      font-size: 14px;
      font-weight: 400;
      color: var(--theme-color);
      line-height: 20px;
    }

    &-content {
      &__item {
        height: 44px;
        font-size: 14px;
        color: var(--base-font-color);
        line-height: 20px;
        padding: 0 16px 0 10px;
      }
    }

    .right-arrow-icon {
      margin: 0 4px;
    }

    :deep(.van-tabs__wrap) {
      padding: 0 16px;
      border-top: 1px solid #f0f2f4;
      border-bottom: 1px solid #f0f2f4;

      .van-tabs__nav {
        padding: 0;
      }

      .van-tab {
        flex: none;
        padding: 0;
      }

      .van-tabs__line {
        display: none;
      }
    }

    :deep(.van-tabs__content) {
      flex: 1 1 0;

      .van-swipe-item {
        overflow: auto;
      }
    }
  }

  &__checkbox {
    width: 28px;
    height: 28px;
    margin-right: 4px;
  }

  &__empty-wrap {
    height: 396px;
  }
}
</style>

<script setup lang="ts">
import { useAttrs, useSlots, ref, computed, watch, nextTick } from 'vue'

// components
import { JbMobilePopup, JbMobileEmpty, JbMobileSearchInput, JbMobileButton } from 'jb-mobile-ui'
import JbMobileTabs from './JbMobileTabs.vue'

const attrs = useAttrs()
const slots = useSlots()
const emits = defineEmits(['update:modelValue', 'confirm'])
const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  dataList: {
    type: Array<Record<string, any>>,
    default: []
  },
  selectedList: {
    type: Array<string>,
    default: []
  },
  beforeConfirm: {
    type: Function,
    default: () => true
  },
})

const bindTypeTabsList = [
  { label: '未绑定', value: 0 },
  { label: '已绑定', value: 1 },
]

const isShowPopup = ref(false) // 是否显示弹出层
const bindType = ref(0) // 绑定类型
const searchValue = ref('') // 搜索的群聊名称
const selectedGroupChat = ref<Record<string, any>>({}) // 选中的选项

const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
const filterDataList = computed(() => props.dataList.filter(item => item.isBound === bindType.value && item.name?.indexOf(searchValue.value) > -1))
// 默认选中的选项
const defaultSelectedGroupChat = computed(() => props.dataList.find(item => item.wechatGroupId === modelValue.value) || {})
const selectedList = computed(() => props.selectedList.filter((item: string) => item !== modelValue.value))

watch(() => isShowPopup.value, (newVal) => {
  if (newVal) {
    selectedGroupChat.value = defaultSelectedGroupChat.value
  }
})

// 选择群聊
const handleSelectGroupChat = (item: Record<string, any>) => {
  if (selectedList.value.includes(item.wechatGroupId)) return
  selectedGroupChat.value = item
}

// 确定
const handleConfirm = () => {
  if (typeof props.beforeConfirm === 'function') {
    const result = props.beforeConfirm(selectedGroupChat.value)
    if (!result) return
  }
  isShowPopup.value = false
  modelValue.value = selectedGroupChat.value.wechatGroupId || ''
  nextTick(() => {
    emits('confirm', defaultSelectedGroupChat.value)
  })
}

// 重置
const handleReset = () => {
  searchValue.value = ''
  selectedGroupChat.value = {}
}
</script>

<template>
  <div
    v-if="slots.reference"
    @click="isShowPopup = true"
  >
    <slot name="reference"></slot>
  </div>
  <div
    v-else
    class="select-group-chat"
    @click="isShowPopup = true"
  >
    <div class="select-group-chat__value ellipsis">{{ defaultSelectedGroupChat?.name || placeholder }}</div>
    <img class="select-group-chat__select-down-icon" src="@/assets/images/common/select-down.png" alt="" />
  </div>
  <jb-mobile-popup
    v-model:visible="isShowPopup"
    v-bind="attrs"
    title="请选择群聊"
  >
    <div class="select-group-chat__popup-content">
      <jb-mobile-tabs
        v-model="bindType"
        :tabs-list="bindTypeTabsList"
        padding="0 0 16px 0"
      ></jb-mobile-tabs>
      <jb-mobile-search-input
        v-model="searchValue"
        placeholder="搜索群聊名称"
      ></jb-mobile-search-input>
      <div class="select-group-chat__list">
        <template v-if="filterDataList.length">
          <div
            v-for="item in filterDataList"
            :key="item.wechatGroupId"
            class="select-group-chat__list-item flex-align-center"
            :class="{
              selected: item.wechatGroupId === selectedGroupChat.wechatGroupId,
              disabled: selectedList.includes(item.wechatGroupId)
            }"
            @click="handleSelectGroupChat(item)"
          >
            <div class="select-group-chat__list-item__name flex-1 ellipsis">{{ item.name }}</div>
            <img class="tick-icon" src="@/assets/images/svg/tick-icon.svg" alt="" />
          </div>
        </template>
        <jb-mobile-empty v-else></jb-mobile-empty>
      </div>
    </div>
    <template #footButton>
      <jb-mobile-button
        block
        round
        @click="handleReset"
      >重置</jb-mobile-button>
      <jb-mobile-button
        type="primary"
        block
        round
        @click="handleConfirm"
      >确定</jb-mobile-button>
    </template>
  </jb-mobile-popup>
</template>

<style scoped lang="less">
.select-group-chat {
  height: 36px;
  padding: 0 12px;
  background-color: #ffffff;
  border: 1px solid #e1e2e3;
  border-radius: 6px;
  box-sizing: border-box;
  display: flex;
  align-items: center;

  &__value {
    flex: 1 1 0;
    font-size: 14px;
    color: var(--base-font-color);
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-right: 12px;
  }

  &__select-down-icon {
    width: 16px;
    height: 16px;
  }

  &__popup-content {
    padding: 0 10px;
  }

  &__list {
    height: 352px;
    padding: 0 6px;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    &-item {
      height: 44px;
      font-size: 14px;
      color: var(--base-font-color);
      line-height: 20px;

      &.disabled {
        color: #d4d9e2;
      }

      &.selected {
        color: var(--theme-color);

        .tick-icon {
          display: block;
        }
      }
    }

    .tick-icon {
      width: 24px;
      height: 24px;
      display: none;
    }
  }
}
</style>

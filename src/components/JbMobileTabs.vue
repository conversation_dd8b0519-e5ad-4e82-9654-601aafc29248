<script setup lang="ts">
/**
 * 标签页组件，目前只有卡片的样式风格
 */
import { computed } from 'vue'

const emits = defineEmits([
  /**
   * 更新标签页所选择标签的值
   */
  'update:modelValue',
  /**
   * 选择某个标签时触发，返回值为所选择标签的数据对象
   */
  'change'
])
const props = defineProps({
  /**
   * 标签页所选择标签的值，可通过 v-model 进行双向数据绑定
   */
  modelValue: {
    type: [Number, String],
    default: ''
  },
  /**
   * 标签页的标签列表
   */
  tabsList: {
    type: Array<Record<string, any>>,
    default: []
  },
  /**
   * 标签数据项的自定义字段，可修改的字段有 label 和 value
   */
  fieldNames: {
    type: Object,
    default: () => ({})
  },
  /**
   * 标签页的内边距
   */
  padding: {
    type: String,
    default: '16px 0 4px'
  },
  /**
   * 标签页的背景颜色
   */
  bgColor: {
    type: String,
    default: '#ffffff'
  },
})

const modelValue = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emits('update:modelValue', val)
  }
})
const fieldNames = computed(() => {
  return Object.assign({}, {
    label: 'label',
    value: 'value'
  }, props.fieldNames)
})

const handleChangeTabsItem = (item: Record<string, any>) => {
  modelValue.value = item[fieldNames.value.value]
  emits('change', item)
}
</script>

<template>
  <div
    class="jb-mobile-tabs flex-justify-center"
    :style="{
      padding: props.padding,
      backgroundColor: props.bgColor,
    }"
  >
    <div class="jb-mobile-tabs__inner flex-align-center">
      <div
        v-for="item in tabsList"
        :key="item[fieldNames.value]"
        class="jb-mobile-tabs__item"
        :class="{ selected: item[fieldNames.value] === modelValue }"
        @click="handleChangeTabsItem(item)"
      >{{ item[fieldNames.label] }}</div>
    </div>
  </div>
</template>

<style scoped lang="less">
.jb-mobile-tabs {
  font-size: 14px;
  line-height: 20px;

  &__inner {
    background-color: var(--bg-color);
    border-radius: 16px;
  }

  &__item {
    padding: 5px 12px;
    border-radius: 16px;

    &.selected {
      color: #ffffff;
      background-color: var(--theme-color);
    }
  }
}
</style>

<script setup lang="ts">
import { ref, computed, watch, watchEffect, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { debounce } from 'lodash-es'

// types
import type { TOrgData } from '@/types/common'

// stores
import { useUserStore } from '@/stores/user'
import { useApplicationStore } from '@/stores/application'

// components
import { JbMobileTreeSelectPopup } from 'jb-mobile-ui'

// hooks
import { useUser } from '@/hooks/useUser'

// utils
import { getDataFunc, findOrgInfoByOrgId, filterOrgList, parseOrgList, filterTree } from '@/utils/common'

// api
import { getIAMOrgTree, getIAMOrgTreeForComplaint } from '@/api/user'

const emits = defineEmits(['update:visible', 'update:orgName', 'update:data', 'afterLoadOrgList'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orgName: {
    type: String,
    default: ''
  },
  type: {
    type: Number,
    default: 1 // 1 - 通用, 2 - 客诉
  },
  dataPermission: {
    type: Boolean,
    default: false
  },
  permissionHouseId: {
    type: Number,
    default: 0
  },
  hasAllOption: {
    type: Boolean,
    default: true
  },
})

const userStore = useUserStore()
const applicationStore = useApplicationStore()
const { orgInfoList } = storeToRefs(userStore) || {}
const { currentOrgInfo } = storeToRefs(applicationStore) || {}
const { getUserPermissionProject } = useUser()

// 显示和隐藏弹出层
const visible = computed({
  get() {
    return props.visible
  },
  set(val) {
    emits('update:visible', val)
  }
})
// 默认选择的地区或项目的名称
const orgName = computed({
  get() {
    return props.orgName
  },
  set(val) {
    emits('update:orgName', val)
  }
})

const apiMap: Record<string, any> = {
  1: getIAMOrgTree,
  2: getIAMOrgTreeForComplaint,
}

const orgSearchValue = ref('') // 当前搜索的组织架构名称
const orgInfo = orgInfoList.value?.[0] // 当前用户所在的组织架构
const defaultOrgId = ref() // 默认选择的地区或项目的 orgId
const orgOriginalList = ref() // 组织架构原始列表
const filteredOrgList = ref() // 过滤后的组织架构列表
const orgListFieldNames = {
  label: 'orgName',
  value: 'orgId'
}
const orgList = computed(() => {
  if (!orgSearchValue.value) {
    return orgOriginalList.value
  }
  return filteredOrgList.value
})

watch(() => orgSearchValue.value, debounce((newVal) => {
  if (newVal) {
    filteredOrgList.value = filterOrgList(orgOriginalList.value, newVal)
  }
}))

watchEffect(() => {
  defaultOrgId.value = currentOrgInfo.value?.orgId || orgInfo?.organizationId
  orgName.value = currentOrgInfo.value?.orgName || orgInfo?.orgName || '请选择组织架构'
})

// 获取组织架构列表
const getOrgList = async() => {
  let data = getDataFunc(await apiMap[props.type]())
  emits('update:data', data)
  if (data?.length) {
    let headquarters: Record<string, any> = {}, dataList: Record<string, any>[] = [], hasHeadquarters = false
    if (props.dataPermission) {
      const { orgIdList, permissionOrgList } = await getUserPermissionProject(data)
      hasHeadquarters = orgIdList.includes(19759)
      data = permissionOrgList
    }
    if (props.permissionHouseId) {
      data = filterTree(data, [props.permissionHouseId])
    }
    if (props.type === 1) { // 通用
      headquarters = data[0]
      dataList = headquarters?.child?.filter((item: Record<string, any>) => item.child?.length > 0) || []
    } else if (props.type === 2) { // 客诉
      headquarters = {
        orgName: '集团',
        orgId: 19759,
        child: []
      }
      dataList = parseOrgList(data)
    }
    if ((!props.dataPermission && !props.permissionHouseId) || hasHeadquarters) {
      dataList.unshift({
        ...headquarters,
        orgName: '集团',
        orgId: 19759,
        child: []
      })
    }
    if (props.hasAllOption) {
      dataList.forEach((item: Record<string, any>) => {
        item.child.unshift({
          orgName: '全部',
          orgId: -1
        })
      })
    }
    orgOriginalList.value = dataList
    // 如果是直接挂在总部下面的员工，在没有选择过地区和项目时默认选择第一个地区公司的第一个项目
    // if (orgInfo?.organizationPath && [...orgInfo.organizationPath].filter(item => item === '/').length === 4 && !currentOrgInfo.value?.orgId) {
    //   applicationStore.setCurrentOrgInfo(findOrgInfoByOrgId(data, 24990) as TOrgData)
    // }
    if (currentOrgInfo.value?.orgId) {
      const orgInfo = findOrgInfoByOrgId(dataList as TOrgData[], currentOrgInfo.value?.orgId as number)
      if (orgInfo === null) {
        applicationStore.setCurrentOrgInfo(props.hasAllOption ? dataList[0] as TOrgData : dataList[0].child?.[0] as TOrgData)
      }
    } else {
      applicationStore.setCurrentOrgInfo(props.hasAllOption ? dataList[0] as TOrgData : dataList[0].child?.[0] as TOrgData)
    }
  }
  emits('afterLoadOrgList')
}

// 确认
const handleConfirm = (value: TOrgData[]) => {
  applicationStore.setCurrentOrgInfo(value[1].orgId === -1 ? (value[0].orgId === 19759 ? { ...value[0], child: orgList.value.slice(1) } : value[0]) : value[1])
}

onMounted(async() => {
  await getOrgList()
})
</script>

<template>
  <jb-mobile-tree-select-popup
    v-model:visible="visible"
    v-model:search-value="orgSearchValue"
    :padding-top="42"
    :data-list="orgList"
    :field-names="orgListFieldNames"
    :default-selected-value="defaultOrgId"
    :is-auto-select-first-children="!orgSearchValue"
    search-input-placeholder="搜索地区、项目名称"
    @confirm="handleConfirm"
  ></jb-mobile-tree-select-popup>
</template>

<style scoped lang="less">

</style>

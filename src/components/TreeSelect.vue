<script setup lang="ts">
import { useAttrs, ref } from 'vue'

const attrs = useAttrs()
const emits = defineEmits(['afterLoadOrgList'])
const props = defineProps({
    disabled: {
      type: Boolean,
      default: false
    }
})

// components
import SelectProjectPopup from '@/components/SelectProjectPopup.vue'

const orgName = ref('请选择组织架构') // 当前选择的地区或项目在组织树的路径
const isShowSelectProjectPopup = ref(false) // 是否显示选择项目弹出层

const showSelectProjectPopup = () => {
  if (props.disabled) return
  isShowSelectProjectPopup.value = true
}
</script>
<template>
    <div class="company-header">
        <div class="company-info">
            <img src="@/assets/images/svg/location.svg" alt=""/>
            <span class="company-name ellipsis" @click="showSelectProjectPopup">{{ orgName }}</span>
            <!-- <van-icon name="arrow-down" /> -->
            <img v-if="!disabled" src="@/assets/images/svg/pull-down.svg" alt="" />
        </div>
    </div>
    <select-project-popup
      v-model:orgName="orgName"
      v-model:visible="isShowSelectProjectPopup"
      v-bind="attrs"
      @afterLoadOrgList="emits('afterLoadOrgList')"
    ></select-project-popup>
</template>
<style lang="less" scoped>
.company-header {
    padding: 9px 16px;
    background-color: #ffffff;
    border-bottom: 1px solid #f9f9f9;
    position: relative;
    z-index: 9999;
    .company-info {
        font-weight: 500;
        font-size: 16px;
        color: var(--base-font-color);
        display: flex;
        align-items: center;
        font-family: base-font-family;
        .company-name {
            margin: 0 4px 0 6px;
        }
    }
}
</style>
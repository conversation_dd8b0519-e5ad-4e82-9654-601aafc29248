import { getToken } from "jinbi-utils";
import { ssoLogin, getIamUserId, getXuanlanUserId } from "@/api/login";
import { useUserStore } from "@/stores/user";
import type { Router } from "vue-router";
import { handleFlatStr } from "@/utils/common";

// 防止重复处理的标记
let processingAuthCode = false;
let processedUrls = new Set<string>();

// 公共路由守卫逻辑
export const setupRouterGuard = (router: Router) => {
  router.beforeEach(async (to, from, next) => {
    // 1. 优先处理/detail路径的state参数，无论是否登录都需要处理
    if (to.path.endsWith("/detail") && to.query.state) {
      try {
        // 先尝试直接解析原始state，如果成功则不需要处理
        const originalState = to.query.state as string;
        let needsProcessing = true;
        
        // 尝试直接解析（处理简单的一次编码）
        try {
          const parsedState = JSON.parse(decodeURIComponent(originalState));
          // 如果解析成功且有id字段，说明state格式正确，无需处理
          if (parsedState && typeof parsedState === 'object' && parsedState.id) {
            needsProcessing = false;
          }
        } catch {
          // 解析失败，需要扁平化处理
          needsProcessing = true;
        }
        
        if (needsProcessing) {
          // 使用扁平化处理多重编码的state
          const flatStr = handleFlatStr("state", originalState);
          const expectedEncoded = encodeURIComponent(flatStr);
          
          // 只有在格式确实不正确时才重写
          if (expectedEncoded !== originalState) {
            try {
              // 解析扁平化后的对象，直接作为query参数
              const stateObj = JSON.parse(flatStr);
              const cleanUrl = new URL(window.location.href);
              
              // 移除原始的state参数
              cleanUrl.searchParams.delete("state");
              
              // 将对象属性作为独立的query参数
              if (stateObj && typeof stateObj === 'object') {
                Object.keys(stateObj).forEach(key => {
                  if (stateObj[key] !== undefined && stateObj[key] !== null) {
                    cleanUrl.searchParams.set(key, String(stateObj[key]));
                  }
                });
              }
              
              window.history.replaceState({}, "", cleanUrl.toString());
              
              // 构建新的query对象
              const newQuery = { ...to.query };
              delete newQuery.state;
              Object.assign(newQuery, stateObj);
              
              return next({
                ...to,
                query: newQuery,
              });
            } catch (parseError) {
              console.error('解析扁平化state失败:', parseError);
              // 降级到原有逻辑
              const cleanUrl = new URL(window.location.href);
              cleanUrl.searchParams.set("state", expectedEncoded);
              window.history.replaceState({}, "", cleanUrl.toString());
              return next({
                ...to,
                query: { ...to.query, state: expectedEncoded },
              });
            }
          }
        }
      } catch (error) {
        console.error('处理state参数失败:', error);
        // 发生错误时继续正常路由
      }
    }

    // 2. 本地开发模式处理
    if (import.meta.env.VITE_APP_CURRENT_MODE === "local") {
      if (!getToken("both")) {
        try {
          const res = await getXuanlanUserId(import.meta.env.VITE_APP_USER_ID);
          const { code, data } = res.data;
          if (code === "00000") {
            const userStore = useUserStore();
            const { token, orgInfoList, userInfo, roleInfoList, userSuffix } =
              data;
            sessionStorage.setItem("token", token);
            userStore.setOrgInfoList(orgInfoList);
            userStore.setUserInfo(userInfo);
            userStore.setRoleInfoList(roleInfoList);
            userStore.setUserSuffix(userSuffix);
            return next();
          }
        } catch (error) {
          console.error("获取本地用户信息失败:", error);
          return next(false);
        }
      }
      return next();
    }

    // 3. 生产环境认证逻辑
    if (to.query.isCustom && parseInt(to.query.isCustom as string) === 1) {
      return next();
    }

    const hasToken = getToken("both");
    const hasAuthCode = to.query?.code;

    // 优先检查是否已有token
    if (hasToken) {
      // 如果有token但URL中还有code参数，清理URL
      if (hasAuthCode && !processingAuthCode) {
        const cleanUrl = new URL(window.location.href);
        cleanUrl.searchParams.delete("code");
        window.history.replaceState({}, "", cleanUrl.toString());
      }
      return next();
    }

    // 没有token的情况
    if (!hasAuthCode) {
      // 没有token且没有授权码，跳转到SSO登录
      try {
        let cleanQuery: any = {};
        
        // 处理state参数，避免多重编码
        if (to.query && Object.keys(to.query).length) {
          cleanQuery = { ...to.query };
          
          // 如果存在state参数，先解码处理
          if (to.query.state && typeof to.query.state === 'string') {
            try {
              const flatStr = handleFlatStr("state", to.query.state);
              const stateObj = JSON.parse(flatStr);
              
              // 移除原始state参数，将解析后的对象展开到query中
              delete cleanQuery.state;
              Object.assign(cleanQuery, stateObj);
            } catch (error) {
              console.warn('解析state参数失败，保持原样:', error);
              // 如果解析失败，保持原始state参数
            }
          }
        }
        
        const query = Object.keys(cleanQuery).length
          ? encodeURIComponent(JSON.stringify(cleanQuery))
          : "";
          
        const res = await ssoLogin(query);
        if (res.data.code === "00000") {
          window.location.href = res.data.data;
          return;
        }
      } catch (err) {
        console.error("SSO登录失败:", err);
        return next(false);
      }
    } else if (hasAuthCode && !processingAuthCode) {
      // 有授权码且未在处理中，先清理URL中除code外的所有参数，然后进行token获取流程
      console.log("开始处理授权码:", hasAuthCode);
      
              // 生成当前URL的标识，用于防止重复处理（确保键顺序一致）
        const sortedQuery = Object.keys(to.query).sort().reduce((result: any, key) => {
          result[key] = to.query[key];
          return result;
        }, {});
        const currentUrlKey = to.path + JSON.stringify(sortedQuery);
        console.log("🔍 检查URL是否已处理:", processedUrls.has(currentUrlKey));
      
      // 清理URL，只保留code参数，但如果有state参数则解码并保留其内容
      // 防止重复处理同一个URL
      if (!processedUrls.has(currentUrlKey) && (Object.keys(to.query).length > 1 || (Object.keys(to.query).length === 1 && !to.query.code))) {
        // 标记当前URL已处理
        processedUrls.add(currentUrlKey);
        const cleanUrl = new URL(window.location.href);
        const codeValue = to.query.code as string;
        let decodedParams: any = {};
        
        console.log("🔍 处理query参数:", to.query);
        
        // 如果存在state参数，尝试解码
        if (to.query.state && typeof to.query.state === 'string') {
          try {
            const flatStr = handleFlatStr("state", to.query.state);
            
            // 处理可能的URL参数拼接，如 {"id":"123"}?wxkey=JBWY
            let cleanJsonStr = flatStr;
            const questionIndex = flatStr.indexOf('?');
            if (questionIndex !== -1) {
              cleanJsonStr = flatStr.substring(0, questionIndex);
            }
            
            decodedParams = JSON.parse(cleanJsonStr);
            console.log("✅ state参数解码成功:", decodedParams);
          } catch (error: any) {
            console.error('❌ 解码state参数失败:', error?.message);
          }
        }
        
        // 清理所有参数
        cleanUrl.search = '';
        // 添加code参数
        cleanUrl.searchParams.set('code', codeValue);
        
        // 保留当前query中除了code和state之外的所有参数
        Object.keys(to.query).forEach(key => {
          if (key !== 'code' && key !== 'state' && to.query[key] !== undefined && to.query[key] !== null) {
            cleanUrl.searchParams.set(key, String(to.query[key]));
          }
        });
        
        // 如果有从state解码出来的新参数，也添加进去（但不覆盖已存在的）
        Object.keys(decodedParams).forEach(key => {
          if (decodedParams[key] !== undefined && decodedParams[key] !== null && !cleanUrl.searchParams.has(key)) {
            cleanUrl.searchParams.set(key, String(decodedParams[key]));
          }
        });
        
        // 构建最终的query对象
        const finalQuery: any = { code: codeValue };
        
        // 保留除了code和state之外的所有现有参数
        Object.keys(to.query).forEach(key => {
          if (key !== 'code' && key !== 'state' && to.query[key] !== undefined && to.query[key] !== null) {
            finalQuery[key] = to.query[key];
          }
        });
        
        // 添加从state解码出来的新参数（但不覆盖已存在的）
        Object.keys(decodedParams).forEach(key => {
          if (decodedParams[key] !== undefined && decodedParams[key] !== null && !(key in finalQuery)) {
            finalQuery[key] = decodedParams[key];
          }
        });
        
        console.log("✅ URL清理完成 - 最终参数:", finalQuery);
        
        window.history.replaceState({}, "", cleanUrl.toString());
        
        // 重新导航到清理后的URL
        return next({
          ...to,
          query: finalQuery
        });
      } else if (processedUrls.has(currentUrlKey)) {
        console.log("🔍 URL已处理过，跳过重复处理");
      }
      
      processingAuthCode = true;

      try {
        const res = await getIamUserId(to.query?.code as string);
        if (res.data.code === "00000") {
          const subRes = await getXuanlanUserId(res.data.data);
          const { code, data } = subRes.data;
          if (code === "00000") {
            const userStore = useUserStore();
            const { token, orgInfoList, userInfo, roleInfoList, userSuffix } =
              data;
            sessionStorage.setItem("token", token);
            userStore.setOrgInfoList(orgInfoList);
            userStore.setUserInfo(userInfo);
            userStore.setRoleInfoList(roleInfoList);
            userStore.setUserSuffix(userSuffix);

            console.log("token获取成功，清理URL并重定向");

            const cleanUrl = new URL(window.location.href);
            cleanUrl.searchParams.delete("code");
            processingAuthCode = false;
            // 清理已处理的URL集合，避免内存泄漏
            processedUrls.clear();
            window.location.replace(cleanUrl.toString());
            return;
          }
        }

        console.error("获取token失败");
        processingAuthCode = false;
        return next(false);
      } catch (error) {
        console.error("处理授权码失败:", error);
        processingAuthCode = false;
        return next(false);
      }
    } else if (processingAuthCode) {
      console.log("正在处理授权码，等待...");
      return next(false);
    }

    console.log("其他情况，继续路由");
    return next();
  });
};

import { storeToRefs } from 'pinia'
import { cloneDeep } from 'lodash-es'

// types
import type { TOrgData } from '@/types/common'

// stores
import { useUserStore } from '@/stores/user'

// utils
import { getDataFunc, filterTree } from '@/utils/common'

// api
import { getUserProjectInfo } from '@/api/user'

export const useUser = () => {
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore) || {}

  // 获取登录用户的数据权限
  const getUserPermissionProject = async(list: TOrgData[]) => {
    const data = getDataFunc(await getUserProjectInfo({
      userId: userInfo.value?.userId,
    }))
    const houseIdList = data?.map((item: Record<string, any>) => item.houseId) || []

    return {
      orgIdList: data?.map((item: Record<string, any>) => item.orgId) || [],
      houseIdList,
      permissionOrgList: filterTree(cloneDeep(list), houseIdList),
    }
  }

  return {
    getUserPermissionProject
  }
}

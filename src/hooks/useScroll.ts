import { throttle } from 'lodash-es'
import { ref, onMounted, onUnmounted } from 'vue'

interface ScrollOptions {
  className?: string
  initialThreshold?: number
  onReach?: Function | null
  direction?: string
  throttleTime?: number
}

export default function useScroll(options: ScrollOptions = {}) {
  const {
    className = '',
    initialThreshold = 0,
    onReach = null,
    direction = 'vertical',
    throttleTime = 200,
  } = options

  const threshold = ref(options.initialThreshold || 0)
  let target: any

  const scrollPosition = ref(0)
  const isReached = ref(false)
  let rafId: number | null

  const getScrollPosition = () => {
    if (target === window) {
      return direction === 'vertical' ? window.pageYOffset : window.pageXOffset
    } else {
      return direction === 'vertical' ? target.scrollTop : target.scrollLeft
    }
  }

  const updateThreshold = (val: number) => {
    threshold.value = val
  }

  const checkThreshold = () => {
    const currentPos = getScrollPosition()
    scrollPosition.value = currentPos
    // 实时检测是否达到阈值
    const newReached = currentPos >= threshold.value
    if (newReached !== isReached.value) {
      isReached.value = newReached
      if (onReach) onReach(newReached)
    }
  }

  const handleScroll = throttle(() => {
    if (rafId) return
    rafId = requestAnimationFrame(() => {
      checkThreshold()
      rafId = null
    })
  }, throttleTime)

  onMounted(() => {
    target = className
      ? (document.querySelector(`.${className}`) as HTMLElement)
      : window
    console.log(target)

    target.addEventListener('scroll', handleScroll)
    // checkThreshold() // 初始化检测
  })

  onUnmounted(() => {
    target.removeEventListener('scroll', handleScroll)
    if (rafId) cancelAnimationFrame(rafId)
  })

  return {
    scrollPosition,
    isReached,
    checkThreshold, // 暴露手动检测方法
    updateThreshold,
  }
}

import {getWecomTicket} from "@/api/wecom.ts";
import {init} from "@/utils/common.ts";

const getJsapiTicket = async (data: unknown) => {
    const res = await getWecomTicket(data)
    const {code, data: ticket} = res?.data || {}
    if (code === '00000') {
        await init({jsapiTicket: ticket})
    }
}
const initWecom = async () => {
    const {VITE_APP_CORP_ID, VITE_APP_WECOM_SECRET} = import.meta.env;
    await getJsapiTicket({corpId: VITE_APP_CORP_ID, secret: VITE_APP_WECOM_SECRET})
}

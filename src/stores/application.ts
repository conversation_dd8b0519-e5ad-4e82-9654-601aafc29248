import {ref} from 'vue'
import {defineStore} from 'pinia'

// types
import type {TOrgData} from '@/types/common'

// utils
import {findOrgIdsByOrgId} from '@/utils/common'

export const useApplicationStore = defineStore('application', () => {
    const currentOrgInfo = ref<TOrgData>() // 选择某个地区或项目后的地区或项目信息
    const currentHouseIdList = ref<number[] | string[]>([]) // 选择某个地区或项目后的 houseId 列表
    const authHouseList = ref<string[] | number[]>([]) // 所有有权限的项目列表
    const setCurrentOrgInfo = (info: TOrgData) => {
        if (info && Object.keys(info).length > 0) {
            const {houseId} = info || {}
            const res = findOrgIdsByOrgId(info, houseId!, 'houseId') as number[]
            if (res && res.length > 0) {
                currentHouseIdList.value = Array.from(new Set(res.filter(item => item))) || []
            }
        }
        currentOrgInfo.value = info
    }

    return {
        currentOrgInfo,
        currentHouseIdList,
        setCurrentOrgInfo,
        authHouseList
    }
}, {
    persist: {
        // @ts-ignore
        paths: [
            'currentOrgInfo',
            'currentHouseIdList',
            'authHouseList'
        ],
        storage: localStorage
    }
})

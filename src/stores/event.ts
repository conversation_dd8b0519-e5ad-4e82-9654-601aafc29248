import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useEventStore = defineStore('event', () => {
  const dialogHouseId = ref<string>(0)
  const dialogGroupList = ref<Record<string, any>[]>([])

  const setDialogHouseId = (value: string) => {
    dialogHouseId.value = value
  }

  const setDialogGroupList = (value: Record<string, any>[]) => {
    dialogGroupList.value = value
  }

  return {
    dialogHouseId,
    dialogGroupList,
    setDialogHouseId,
    setDialogGroupList,
  }
}, {
  persist: {
    // @ts-ignore
    paths: [
      'dialogHouseId',
      'dialogGroupList',
    ],
    storage: localStorage
  }
})

import { ref } from 'vue'
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', () => {
  const orgInfoList = ref<Record<string, any>[]>([])
  const userInfo = ref<Record<string, any>>({})
  const roleInfoList = ref<Record<string, any>[]>([])
  const isAreaRole = ref(false) // 当前登录用户是否为地区/项目特殊角色
  const userSuffix = ref('') // 登录用户手机号后四位

  const setOrgInfoList = (value: Record<string, any>[]) => {
    orgInfoList.value = value
  }

  const setUserInfo = (value: Record<string, any>) => {
    userInfo.value = value
  }

  const setRoleInfoList = (value: Record<string, any>[]) => {
    roleInfoList.value = value
    isAreaRole.value = value?.some((item: Record<string, any>) => item.userType === '1') && false
  }

  const setUserSuffix = (value: string) => {
    userSuffix.value = value
  }

  return {
    orgInfoList,
    userInfo,
    roleInfoList,
    isAreaRole,
    userSuffix,
    setOrgInfoList,
    setUserInfo,
    setRoleInfoList,
    setUserSuffix,
  }
}, {
  persist: {
    // @ts-ignore
    paths: [
      'orgInfoList',
      'userInfo',
      'roleInfoList',
      'isAreaRole',
      'userSuffix',
    ],
    storage: localStorage
  }
})

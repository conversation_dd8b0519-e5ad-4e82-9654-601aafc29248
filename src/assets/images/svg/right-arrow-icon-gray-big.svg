<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>图标（16）/更多/小</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="请选择地块" transform="translate(-66, -341)">
            <g id="编组-6" transform="translate(0, 272)">
                <g id="编组-5" transform="translate(16, 67)">
                    <g id="图标（16）/更多/小" transform="translate(50, 2)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <polyline id="路径-4" stroke="#999999" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" transform="translate(7.8461, 8.1731) rotate(-90) translate(-7.8461, -8.1731)" points="3.5 6 7.8461324 10.3461324 12.1922648 6"></polyline>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>图标（16）/编辑</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="工单进行中" transform="translate(-80, -484)">
            <g id="编组-17" transform="translate(0, 209)">
                <g id="编组-9" transform="translate(16, 148)">
                    <g id="编组-8" transform="translate(16, 16)">
                        <g id="编组-15" transform="translate(20, 110)">
                            <g id="图标（16）/编辑" transform="translate(28, 1)">
                                <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                                <g id="编组" transform="translate(2.434, 1.272)" stroke="#FF6824" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M10.6666667,6.33333333 L10.6666667,3.33333333 L7.66666667,0 L0.666666667,0 C0.298476667,0 0,0.298476667 0,0.666666667 L0,12.6666667 C0,13.0348667 0.298476667,13.3333333 0.666666667,13.3333333 L4.66666667,13.3333333" id="路径"></path>
                                    <polygon id="路径" points="8 13.3333333 11.3333333 10 10 8.66666667 6.66666667 12 6.66666667 13.3333333"></polygon>
                                    <polyline id="路径" points="7.33333333 0 7.33333333 3.33333333 10.6666667 3.33333333"></polyline>
                                </g>
                                <rect id="codesign_placeholder_name" x="0" y="0" width="16" height="16"></rect>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>图标（28）/勾选/未选择</title>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="请选择地块" transform="translate(-10, -424)">
            <g id="编组-6" transform="translate(0, 272)">
                <g id="编组-3" transform="translate(0, 100)">
                    <g id="编组-30备份" transform="translate(0, 44)">
                        <g id="图标（28）/勾选/未选择" transform="translate(10, 8)">
                            <rect id="矩形" x="0" y="0" width="28" height="28"></rect>
                            <rect id="矩形" stroke="#7B52FE" stroke-width="1.5" fill="#7B52FE" x="4.75" y="4.75" width="18.5" height="18.5" rx="4"></rect>
                            <polyline id="路径" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" points="10 14.5 12.6666667 17 18 12"></polyline>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>
<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>图标（24）/火备份</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF5124" stop-opacity="0.611150568" offset="0%"></stop>
            <stop stop-color="#FF5124" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="AI每日新鲜事" transform="translate(-115, -99)">
            <g id="编组-10" transform="translate(16, 99)">
                <g id="图标（24）/火备份" transform="translate(99, 0)">
                    <rect id="矩形" x="0" y="0" width="24" height="24"></rect>
                    <path d="M8.05729529,22.2582403 C8.43595937,20.9089148 9.01526185,20.4591397 9.79520274,20.9089148 C10.9651141,21.5835776 18.0967532,23.389472 20.2732286,17.1665077 C21.5371613,13.6204743 21.025697,10.3789194 18.7388357,7.44184287 L17.4606213,10.7546081 C16.3006278,5.01355284 13.5955302,1.82575272 9.34532839,1.19120773 C8.15393447,0.528597582 9.67916372,3.60779467 8.05729529,5.52481877 C6.43542685,7.44184287 1.81499115,12.1815905 3.63115056,16.5452527 C4.8419235,19.4543608 6.31730508,21.35869 8.05729529,22.2582403 Z" id="路径-10" fill="url(#linearGradient-1)"></path>
                </g>
            </g>
        </g>
    </g>
</svg>
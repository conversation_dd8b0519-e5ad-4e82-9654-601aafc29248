.p-16 {
  padding: 16px;
}
.p-12 {
  padding: 12px;
}
.p-8 {
  padding: 8px;
}
.p-4 {
  padding: 4px;
}
.p-2 {
  padding: 2px;
}
.mt-16 {
  margin-top: 16px;
}
.mt-12 {
  margin-top: 12px;
}
.mt-8 {
  margin-top: 8px;
}
.mt-4 {
  margin-top: 4px;
}
.mt-2 {
  margin-top: 2px;
}
.mb-16 {
  margin-bottom: 16px;
}
.mb-12 {
  margin-bottom: 12px;
}
.br-4 {
  border-radius: 4px;
}
.br-8 {
  border-radius: 8px;
}
.br-16 {
  border-radius: 16px;
}
.base-font-family {
  font-family: "PingFang SC", "Noto Sans SC", "Microsoft YaHei" !important;
}
.common-icon {
  width: 16px;
  height: 16px;
}
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.overflow-hidden {
  overflow: hidden;
}
.sticky-top {
  position: sticky;
  left: 0;
  top: 0;
  z-index: 10;
}
.purple {
  color: var(--theme-color);
}
.yellow {
  color: #ff970f;
}
.red {
  color: #fb3f1f;
}
.font-weight-500 {
  font-weight: 500;
}

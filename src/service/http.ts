import { getToken } from 'jinbi-utils'
// const currentMode = import.meta.env.VITE_APP_CURRENT_MODE
// const request = http({cacheType: 'sessionStorage',currentMode, errorCb: () => {},getTokenCb: () => {},tokenKey: 'token'})

import axios, { type AxiosResponse } from 'axios'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const errorCode = ['499', '00499', '401', '00401']

const axiosPlugin = axios.create({
  // baseURL: 'http://172.18.71.196:29999',
  timeout: 60000,
  headers: {
    'Content-Type': 'application/json',
  },
})
axiosPlugin.defaults.withCredentials = true

const resetFunc = async () => {
  sessionStorage.clear()
  const router = useRouter()
  await router?.replace({
    path: '/index',
    force: true,
  })
}

// 请求拦截器
axiosPlugin.interceptors.request.use(config => {
  config.headers.FrontType = 'egc-admin-ui'
  config.headers.Authorization = getToken('both') || ''
  if (config.method === 'get') {
    config.params = {
      ...config.params
    }
  }
  return config
}, error => {
  return Promise.reject(error)
})

// 响应拦截器
axiosPlugin.interceptors.response.use((response: AxiosResponse) => {
  const statusCode = response.data.code
  const msgCode = response.data.msgCode
  const res = response.data
  const errMsg = response.data.message
  const config = response.config

  // blob文件流
  if (res instanceof Blob) {
    return response
  }
  if (typeof res === 'string') {
    return response
  }
  // ArrayBuffer文件流
  if (res instanceof ArrayBuffer) {
    return response
  }
  if (statusCode === '00000' || statusCode === 1) { // 1 为 bi 接口所返回的 code
    return response
  }
  if (msgCode === 200) {
    return response.data
  }
  if (statusCode !== '00401' && !config?.params?.hideErrorMsg) {
    showToast(errMsg || '网络请求失败')
  }
  if (statusCode === '00401') {
    return resetFunc()
  }
  return Promise.reject(new Error(errMsg || 'Error'))
}, async function (error) {
  console.log('error', error)
  const { data } = error.response || {}
  const { code } = data || {}
  // token 过期
  if (errorCode.includes(code)) {
    await resetFunc()
  }
  // 取消未返回的请求
  if (error.code === 'ERR_CANCELED') {
    return error
  }
  return error.response
})

export default axiosPlugin

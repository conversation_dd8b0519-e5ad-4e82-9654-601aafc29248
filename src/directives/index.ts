import { createApp } from 'vue'
import type { Directive } from 'vue'

import Loading from '@/components/Loading.vue'
/**
 * @param {Record<string, any>} el - 挂载的元素
 * @param {string} bgNone - 是否存在背景颜色, bgNone为true，则不显示背景颜色
 * */
const createLoadingInstance = (el: Record<string, any>, bgNone: boolean) => {
  let  isChangePosition = false, originalPosition = ''
  const loadingInstance = createApp(Loading)
  const vm = loadingInstance.mount(document.createElement('div'))
  const close = () => {
    if (isChangePosition) {
      el.style.position = originalPosition
    }
    el.removeChild(vm.$el)
  }
    if (bgNone) {
        vm.$el.style.backgroundColor = 'transparent'
    } else {
        vm.$el.style.backgroundColor = 'rgba(255, 255, 255, .9)'
    }
  if (!['absolute', 'fixed', 'sticky'].includes(el.style.position)) {
    isChangePosition = true
    originalPosition = el.style.position
    el.style.position = 'relative'
  }
  el.appendChild(vm.$el)
  el.customLoading = {
    instance: {
      vm,
      close
    }
  }
}

const vLoading: Directive = {
  mounted(el, binding) {
      console.log('arg:', binding.arg)
      console.log('value:', binding.value)
      console.log('modifiers:', binding.modifiers)
      const { bgNone } = binding.modifiers
    if (binding.value) {
      createLoadingInstance(el,  bgNone)
    }
  },
  updated(el, binding) {
      const { bgNone } = binding.modifiers
    if (binding.oldValue !== binding.value) {
      if (binding.value && !binding.oldValue) { // 显示 loading
        createLoadingInstance(el, bgNone)
      } else {
        el.customLoading?.instance?.close()
      }
    }
  },
  unmounted(el) {
    el.customLoading?.instance?.close()
    el.customLoading = null
  }
}

export {
  vLoading
}

import axiosPlugin from '@/service/http'

const baseUrl = '/ai'
// 获取企微ticket （企业）
export const getWecomTicket = (data: unknown) => {
    return axiosPlugin.post(`${baseUrl}/api/message/wecom/getWecomTicket
`, data)
}
// 获取企微ticket (应用)
export const getWecomAgentTicket = (data: unknown) => {
    return axiosPlugin.post(`${baseUrl}/api/message/wecom/getAgentWecomTicket
`, data)
}
export const uploadFile = (file: FormData) => {
    const formData = new FormData()
    formData.append('file', file)
    return axiosPlugin.post(`${baseUrl}/app/uploadFile`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    })
}

// 全局检索-->查看聊天记录--群聊
export const getGroupChatRecord = (data: unknown) => {
    return axiosPlugin.post(`${baseUrl}/message/searchAllGroupRecord`, data)
}

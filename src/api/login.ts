// 登录接口
import axios from 'axios'
import axiosPlugin from '@/service/http'
import {getConfig} from "@/utils/common.ts";

// 动态导入配置文件
// const getConfig = async () => {
//   const mode = import.meta.env.VITE_APP_CURRENT_MODE;
//   try {
//     if (mode === 'prod') {
//       const config = await import('../../config/prod.config');
//       return config.default;
//     } else {
//       const config = await import('../../config/test.config');
//       return config.default;
//     }
//   } catch (error) {
//     console.error('Failed to load config:', error);
//     // 返回默认配置
//     return {
//         taskGenerateClientId: import.meta.env.VITE_APP_CLIENT_ID,
//         taskGenerateSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
//         complainAnalysisClientId: import.meta.env.VITE_APP_CLIENT_ID,
//         complainAnalysisSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
//         xuanlanMobileSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
//         groupWorkOrderSecret: import.meta.env.VITE_APP_CLIENT_SECRET,
//     };
//   }
// };
interface IConfigParam {
  url: (baseDomain:string) => string
}
// 动态获取 REDIRECT_URI 和对应的 ClientId、Secret
const getDynamicConfig = async ({url}:IConfigParam) => {
  const currentPath = window.location.pathname;
  const baseDomain = import.meta.env.VITE_APP_REDIRECT_BASE_URI || 'https://xuanlan-test-mobile.jinbizhihui.com';
  const config = await getConfig();
  // 根据路径确定页面类型并返回对应配置
  if (currentPath.includes('TaskGenerate') || currentPath.includes('/TaskGenerate/') || currentPath.includes('/ComplaintAnalysis/bind')) {
    return {
      // url: url(baseDomain) || `${baseDomain}/TaskGenerate/`,
      url: url(baseDomain) || `${baseDomain}${currentPath}/`,
      clientId: config.taskGenerateClientId,
      clientSecret: config.taskGenerateSecret
    };
  } else if (currentPath.includes('ComplaintAnalysis') || currentPath.includes('/ComplaintAnalysis/')) {
    return {
      url: url(baseDomain) || `${baseDomain}/ComplaintAnalysis/`,
      clientId: config.complainAnalysisClientId,
      clientSecret: config.complainAnalysisSecret
    };
  }

  // 默认返回 ComplaintAnalysis 配置
  return {
    url: baseDomain,
    clientId: config.complainAnalysisClientId,
    clientSecret: config.complainAnalysisSecret
  };
};
const handleUrl = () => {
    const { pathname, origin } = location
    if (pathname) {
        localStorage.setItem('redirect_pathname', pathname);
    }
    return origin + pathname
}
const {VITE_APP_SSO_LOGIN_HOST, VITE_APP_AUTHORIZE_REQUEST_URL} = import.meta.env;

export const ssoLogin = async (state?: string) => {  
  let finalState = '';
  if (typeof state === 'object' && state !== null) {
    finalState = encodeURIComponent(JSON.stringify(state));
  } else if (typeof state === 'string') {
    try {
      // 尝试decode再parse，能parse说明已转码
      JSON.parse(decodeURIComponent(state));
      finalState = state; // 已转码
      
    } catch {
      try {
        // 尝试直接parse，能parse说明是未转码的JSON字符串
        JSON.parse(state);
        finalState = encodeURIComponent(state);
      } catch {
        // 既不是转码的JSON，也不是未转码的JSON，直接用
        finalState = state;
      }
    }
  } else {
    finalState = '';
  }
  const dynamicConfig = await getDynamicConfig({
    url : handleUrl
  });
    // console.log('dynamicConfig', dynamicConfig)
  return axios.post(`/ai/login/sso/baseRedirect`, {
    clientId: dynamicConfig.clientId,
    redirectUri: encodeURIComponent(dynamicConfig.url),
    authorizeRequestUrl: VITE_APP_AUTHORIZE_REQUEST_URL,
    state: finalState.includes('?wxkey=JBWY') ? finalState : finalState+'?wxkey=JBWY'
  }).then((res) => {
    return res;
  }).catch((err) => {
    throw err; // 重新抛出错误，让调用者能够捕获
  });
};

// 获取iam用户id
export const getIamUserId = async (code: string) => {
  const dynamicConfig = await getDynamicConfig({
    url : () => window.location.href
  });
  // 请求IAM用户ID
  return await axios.post(`${VITE_APP_SSO_LOGIN_HOST}/ai/login/sso/getIamUserId`, {
    clientId: dynamicConfig.clientId, // required
    clientSecret: dynamicConfig.clientSecret, // required
    redirectUri: encodeURIComponent(dynamicConfig.url),
    code
  }) || {};
};

export const getXuanlanUserId = async (data: unknown) => {
  return await axiosPlugin.post('/ai/api/platform/login/sso/get-userid', data, {
    headers: {
      'Content-Type': 'text/plain'
    }
  });
};

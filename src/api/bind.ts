import axiosPlugin from '@/service/http'

const baseUrl = '/ai'

// 获取用户绑定的项目信息
export const getUserBindProject = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/wechatGroup/getUserBindProject`, params)
}

// 获取项目所有的群聊信息
export const getAllGroupInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/wechatGroup/getAllGroupInfo`, params)
}

// 查询群聊绑定的楼栋信息
export const getGroupBindBuildingInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/wechatGroup/getGroupBindBuildingInfo`, params)
}

// 修改群聊绑定的楼栋信息
export const updateGroupBindProject = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/wechatGroup/updateGroupBindProject`, params)
}

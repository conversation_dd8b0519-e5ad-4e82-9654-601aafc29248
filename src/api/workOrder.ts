import axiosPlugin from '@/service/http'

const baseUrl = '/ai'

// 工单统计
export const statistics = (params: Record<string, any>) => {
    return axiosPlugin.post(`${baseUrl}/api/workorder/statistics`, params)
  }
  // 回复工单
export const reply = (params: Record<string, any>) => {
    return axiosPlugin.post(`${baseUrl}/api/workorder/reply`, params)
  }
  // 工单列表
export const list = (params: Record<string, any>) => {
    return axiosPlugin.post(`${baseUrl}/api/workorder/list`, params)
  }
  // 完成工单
export const finish = (params: Record<string, any>) => {
    return axiosPlugin.post(`${baseUrl}/api/workorder/finish`, params)
  }
    // 工单
export const detail = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/workorder/detail`, params)
}
  // 地区与群绑定
export const operateOrganizationRelation = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/workorder/operateOrganizationRelation`, params)
}
// 查询项目信息
export const queryHouseByUserIdList = (params?: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/workorder/queryHouseByUserIdList`, params)
}
// 查询群消息
export const queryWechatGroupList = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/workorder/queryWechatGroupList`, params)
}

//通过当前登入人获取本地权限
export const getUserProjectInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/user/getUserProjectInfo`, params)
}

//获取全部工单群
export const queryOrganizationRelation = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/workorder/queryOrganizationRelation`, params)
}


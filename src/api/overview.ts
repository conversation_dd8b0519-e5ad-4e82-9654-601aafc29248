import axiosPlugin from '@/service/http'

const baseUrl = '/bi'

// 获取项目的收楼户数
export const getTotalAccepted = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/totalAccepted`, params)
}

// 获取项目的收缴率信息
export const getCollectionRate = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/collectionRate`, params)
}

// 获取项目的利润信息
export const getProfit = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/profit`, params)
}

// 获取项目的满意度折线图
export const getSatisfaction = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/satisfaction`, params)
}

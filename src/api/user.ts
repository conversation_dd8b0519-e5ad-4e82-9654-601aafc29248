import axiosPlugin from '@/service/http'

const baseUrl = '/ai'

// 获取组织树
export const getIAMOrgTree = () => {
  return axiosPlugin.get(`${baseUrl}/org/user/getIAMOrgTree`)
}

// 获取客诉的组织树
export const getIAMOrgTreeForComplaint = () => {
  return axiosPlugin.get(`${baseUrl}/api/message/complaints/getComplaintsTree`)
}

// 获取项目的楼栋组织树
export const getProjectBuildingInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/wechatGroup/getProjectBuildingInfo`, params)
}

// 获取用户的数据权限
export const getUserProjectInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/user/getUserProjectInfo`, params)
}

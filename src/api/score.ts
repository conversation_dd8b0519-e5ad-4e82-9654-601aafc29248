import axiosPlugin from '@/service/http'

const baseUrl = '/ai'

// 根据项目 id 查询小区的基本信息
export const getCommunityBaseInfo = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getCommunityBseInfo`, params)
}

// 获取项目分项得分
export const getRateAndAiResult = (params: Record<string, any>, config: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getRateAndAiResult`, params, config)
}

// 获取 AI 评分分析
export const getAiResult = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getScore`, params)
}

// 获取 AI 评分分析的固定提示词
export const getSysPrompt = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getSysPrompt`, params)
}

// 获取项目客诉评分趋势折线图
export const getAiScoreTrendLineChart = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/yearLinetChart`, params)
}

// 获取项目客诉评分、基础服务分、客情关系分的趋势图
export const getScoreTrend = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getScoreTrend`, params)
}

// 获取项目问题排名
export const getTagSecondRank = (params: Record<string, any>) => {
  return axiosPlugin.post(`${baseUrl}/api/message/complaints/getTagSecondRank`, params)
}

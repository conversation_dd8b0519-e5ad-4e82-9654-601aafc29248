import { resolve } from "path";
import { ViteDevServer } from "vite";

interface Page {
  name: string;
  htmlName: string;
  htmlPath: string;
  outPagePath: string;
  redirectUri: string;
  path?: string;
}

// 多页面信息
export let pages: Page[] = [
  {
    name: "ComplaintAnalysis",
    htmlName: "index.html",
    htmlPath: "src/pages/ComplaintAnalysis/",
    outPagePath: "ComplaintAnalysis/",
    redirectUri: "https://xuanlan-test-mobile.jinbizhihui.com/ComplaintAnalysis",
  },
  {
    name: "TaskGenerate",
    htmlName: "index.html",
    htmlPath: "src/pages/TaskGenerate/",
    outPagePath: "TaskGenerate/",
    redirectUri: "https://xuanlan-test-mobile.jinbizhihui.com/TaskGenerate",
  },
];

pages.forEach((page) => {
  page.path = resolve(process.cwd(), page.htmlPath + page.htmlName);
});

// server插件
export const multiplePagePlugin = () => ({
  name: "multiple-page-plugin",
  configureServer(server: ViteDevServer) {
    server.middlewares.use((req: any, res, next) => {
      // console.log("请求地址", req.url);
      for (let page of pages) {
        if (req.url.startsWith(`/${page.name}`)) {
          console.log(
            "-----",
            page.name,
            "------",
            req.url,
            "-----",
            `/${page.htmlPath}${page.htmlName}`
          );

          req.url = `/${page.htmlPath}${page.htmlName}`;
          break;
        }
      }
      next();
    });
  },

});

import { fileURLToPath, URL } from "node:url";
import { pages, multiplePagePlugin } from "./generate";
import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import vueDevTools from "vite-plugin-vue-devtools";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "@vant/auto-import-resolver";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { resolve } from "path";
export default defineConfig({
  optimizeDeps: {
    include: ["echarts"], // 明确指定需要优化的依赖
  },
  server: {
    cors: true,
    host: "0.0.0.0",
    proxy: {
      "/ai": {
        target: "https://xuanlan-test-mobile.jinbizhihui.com",
        changeOrigin: true,
        secure: false,
      },
      "/bi": {
        target: "https://aiapp-test.jinbizhihui.com",
        changeOrigin: true,
        secure: false,
      },
      "/abb": {
        target: "http://************:29999",
        // target: "http://*************:29999",
        changeOrigin: true,
        secure: false,
        // rewrite: (path) => path.replace(/^\/abb/, "/ai"),
        rewrite: (path) => path.replace(/^\/abb/, ""),
      },
    },
  },
  base: "./", // 改为相对路径，这样可以适应任何部署路径
  build: {
    outDir: "xuanlan-mobile",
    minify: "terser",
    terserOptions: {
      compress: {
        //生产环境时移除console
        drop_console: false,
        drop_debugger: true,
      },
    },
    cssCodeSplit: true, // 启用 CSS 代码拆分，以便更好地利用 HTTP/2 多路复用
    sourcemap: false,
    assetsDir: "assets", // 确保assets目录名称一致
    rollupOptions: {
      input: pages.reduce((res: Record<string, string>, cur) => {
        res[cur.name] = cur.path as string;
        return res;
      }, {}),
      output: {
        // 自定义输出目录和文件名
        entryFileNames: (chunkInfo) => {
          // 为每个页面的入口文件放在对应目录下
          const page = pages.find((p) => p.name === chunkInfo.name);
          return page
            ? `${page.outPagePath.replace(/^\//, "")}assets/[name].[hash].js`
            : "assets/[name].[hash].js";
        },
        chunkFileNames: (chunkInfo) => {
          // 根据chunk的依赖关系，判断应该放在哪个页面目录
          // 默认情况下，共享的chunk会被多个入口使用，我们需要为每个页面复制一份
          // 但Rollup不支持直接复制，所以我们采用简化策略：
          // 检查chunk的模块依赖，如果包含特定页面的代码，就放在对应目录

          if (chunkInfo.facadeModuleId) {
            // 检查是否属于特定页面
            for (const page of pages) {
              if (chunkInfo.facadeModuleId.includes(`pages/${page.name}`)) {
                return `${page.outPagePath.replace(/^\//, "")}assets/[name].[hash].js`;
              }
            }
          }

          // 对于共享chunk，我们需要一个策略来决定放在哪里
          // 方案：根据chunk的名称或内容来分配
          const chunkName = chunkInfo.name || '';

          // 如果chunk名称包含页面相关信息
          for (const page of pages) {
            if (chunkName.toLowerCase().includes(page.name.toLowerCase())) {
              return `${page.outPagePath.replace(/^\//, "")}assets/[name].[hash].js`;
            }
          }

          // 默认放在第一个页面目录，但这不是最优解
          // 更好的方案是在后处理阶段复制文件
          return `${pages[0].outPagePath.replace(/^\//, "")}assets/[name].[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          // 静态资源按照类似的逻辑分配
          if (assetInfo.originalFileName) {
            for (const page of pages) {
              if (assetInfo.originalFileName.includes(`pages/${page.name}`)) {
                return `${page.outPagePath.replace(/^\//, "")}assets/[name].[hash][extname]`;
              }
            }
          }

          // 默认放在第一个页面目录
          return `${pages[0].outPagePath.replace(/^\//, "")}assets/[name].[hash][extname]`;
        },
      },
    },
  },
  plugins: [
    vue(),
    vueJsx(),
    vueDevTools({
      launchEditor: "cursor",
    }),
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [VantResolver()],
    }),
    createSvgIconsPlugin({
      // 要缓存的图标文件夹
      iconDirs: [resolve(__dirname, "src/assets/images/svg")],
      // 执行 icon name 的格式
      symbolId: "icon-[name]",
    }),
    multiplePagePlugin(),
  ],
  resolve: {
    alias: {
      "@": fileURLToPath(new URL("./src", import.meta.url)),
      "@dang_8899/xl-ui": "/node_modules/@dang_8899/xl-ui",
      "jinbi-utils": "/node_modules/jinbi-utils/dist/index.esm.js",
      "jb-mobile-ui": "/node_modules/jb-mobile-ui",
    },
  },
});

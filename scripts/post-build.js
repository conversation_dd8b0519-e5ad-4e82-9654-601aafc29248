import fs from 'fs';
import path from 'path';

// 页面配置
const pages = [
  {
    name: "ComplaintAnalysis",
    htmlName: "index.html",
    htmlPath: "src/pages/ComplaintAnalysis/",
    outPagePath: "ComplaintAnalysis/",
  },
  {
    name: "TaskGenerate",
    htmlName: "index.html",
    htmlPath: "src/pages/TaskGenerate/",
    outPagePath: "TaskGenerate/",
  },
];

const distDir = 'xuanlan-mobile';

function copySharedAssets() {
  console.log('📦 复制共享资源到各页面目录...\n');
  
  // 收集所有需要复制的资源
  const sharedAssetsDir = path.join(distDir, pages[0].outPagePath, 'assets');
  
  if (!fs.existsSync(sharedAssetsDir)) {
    console.log('⚠️  共享资源目录不存在，跳过复制');
    return;
  }
  
  const sharedAssets = fs.readdirSync(sharedAssetsDir);
  
  // 为每个页面（除了第一个）复制共享资源
  for (let i = 1; i < pages.length; i++) {
    const page = pages[i];
    const pageAssetsDir = path.join(distDir, page.outPagePath, 'assets');
    
    // 确保目标目录存在
    if (!fs.existsSync(pageAssetsDir)) {
      fs.mkdirSync(pageAssetsDir, { recursive: true });
    }
    
    // 复制所有共享资源
    sharedAssets.forEach(asset => {
      const sourcePath = path.join(sharedAssetsDir, asset);
      const targetPath = path.join(pageAssetsDir, asset);
      
      // 只复制文件，跳过目录
      if (fs.statSync(sourcePath).isFile()) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`   ✅ 复制 ${asset} 到 ${page.name}/assets/`);
      }
    });
  }
  
  console.log('');
}

function processHtmlFiles() {
  pages.forEach(page => {
    const sourcePath = path.join(distDir, page.htmlPath, page.htmlName);
    const targetPath = path.join(distDir, page.outPagePath, page.htmlName);
    
    if (fs.existsSync(sourcePath)) {
      // 读取HTML文件内容
      let htmlContent = fs.readFileSync(sourcePath, 'utf8');
      
      // 修复资源路径 - 每个页面都使用自己的assets目录
      // 将页面特定的assets路径修改为相对路径
      htmlContent = htmlContent.replace(
        new RegExp(`../../../${page.outPagePath}assets/`, 'g'),
        './assets/'
      );
      
      // 将共享的assets路径也修改为当前页面的相对路径
      htmlContent = htmlContent.replace(
        /\.\.\/\.\.\/\.\.\/assets\//g,
        './assets/'
      );
      
      // 修复任何指向其他页面assets的路径
      pages.forEach(otherPage => {
        if (otherPage.name !== page.name) {
          htmlContent = htmlContent.replace(
            new RegExp(`../../../${otherPage.outPagePath}assets/`, 'g'),
            './assets/'
          );
        }
      });
      
      // 确保目标目录存在
      const targetDir = path.dirname(targetPath);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
      
      // 写入修复后的HTML文件
      fs.writeFileSync(targetPath, htmlContent);
      console.log(`✓ 已处理 ${page.name} 页面: ${targetPath}`);
    } else {
      console.warn(`⚠️  源文件不存在: ${sourcePath}`);
    }
  });
  
  // 清理原始的HTML文件
  const srcPagesDir = path.join(distDir, 'src', 'pages');
  if (fs.existsSync(srcPagesDir)) {
    fs.rmSync(srcPagesDir, { recursive: true, force: true });
    console.log('✓ 已清理源文件目录');
  }
}

// 创建.htaccess文件用于Apache服务器
function createHtaccess() {
  const htaccessContent = `# 确保JavaScript模块正确的MIME类型
<FilesMatch "\\.js$">
    Header set Content-Type "application/javascript"
</FilesMatch>

# 启用GZIP压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>`;

  const htaccessPath = path.join(distDir, '.htaccess');
  fs.writeFileSync(htaccessPath, htaccessContent);
  console.log('✓ 已创建 .htaccess 文件');
}

// 创建nginx配置文件示例
function createNginxConfig() {
  const nginxConfig = `# Nginx配置示例
location ~* \\.(js|css)$ {
    add_header Content-Type application/javascript;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location ~* \\.(png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 如果是单页应用，需要配置fallback
location / {
    try_files $uri $uri/ /index.html;
}`;

  const nginxPath = path.join(distDir, 'nginx.conf.example');
  fs.writeFileSync(nginxPath, nginxConfig);
  console.log('✓ 已创建 nginx.conf.example 文件');
}

console.log('开始构建后处理...');
copySharedAssets();
processHtmlFiles();
createHtaccess();
createNginxConfig();
console.log('✓ 构建后处理完成！'); 
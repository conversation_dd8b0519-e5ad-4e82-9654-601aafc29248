import fs from 'fs';
import path from 'path';

const distDir = 'xuanlan-mobile';

function checkDeployment() {
  console.log('🔍 检查部署文件完整性...\n');
  
  // 检查HTML文件
  const htmlFiles = [
    'ComplaintAnalysis/index.html',
    'TaskGenerate/index.html'
  ];
  
  htmlFiles.forEach(htmlFile => {
    const htmlPath = path.join(distDir, htmlFile);
    if (fs.existsSync(htmlPath)) {
      console.log(`✅ ${htmlFile} - 存在`);
      
      // 读取HTML内容并检查资源路径
      const content = fs.readFileSync(htmlPath, 'utf8');
      const jsMatches = content.match(/src="([^"]+\.js)"/g);
      const cssMatches = content.match(/href="([^"]+\.css)"/g);
      
      if (jsMatches) {
        jsMatches.forEach(match => {
          const filePath = match.match(/src="([^"]+)"/)[1];
          console.log(`   📄 JS引用: ${filePath}`);
          
          // 检查文件是否存在
          let actualPath;
          if (filePath.startsWith('./assets/')) {
            actualPath = path.join(distDir, htmlFile.split('/')[0], filePath.substring(2));
          } else if (filePath.startsWith('../assets/')) {
            actualPath = path.join(distDir, filePath.substring(3));
          }
          
          if (actualPath && fs.existsSync(actualPath)) {
            console.log(`      ✅ 文件存在: ${actualPath}`);
          } else if (actualPath) {
            console.log(`      ❌ 文件不存在: ${actualPath}`);
          }
        });
      }
      
      if (cssMatches) {
        cssMatches.forEach(match => {
          const filePath = match.match(/href="([^"]+)"/)[1];
          if (filePath.endsWith('.css')) {
            console.log(`   🎨 CSS引用: ${filePath}`);
            
            // 检查文件是否存在
            let actualPath;
            if (filePath.startsWith('./assets/')) {
              actualPath = path.join(distDir, htmlFile.split('/')[0], filePath.substring(2));
            } else if (filePath.startsWith('../assets/')) {
              actualPath = path.join(distDir, filePath.substring(3));
            }
            
            if (actualPath && fs.existsSync(actualPath)) {
              console.log(`      ✅ 文件存在: ${actualPath}`);
            } else if (actualPath) {
              console.log(`      ❌ 文件不存在: ${actualPath}`);
            }
          }
        });
      }
      
      console.log('');
    } else {
      console.log(`❌ ${htmlFile} - 不存在`);
    }
  });
  
  // 检查关键目录
  const keyDirs = [
    'assets',
    'ComplaintAnalysis/assets',
    'TaskGenerate/assets'
  ];
  
  console.log('📁 检查关键目录:');
  keyDirs.forEach(dir => {
    const dirPath = path.join(distDir, dir);
    if (fs.existsSync(dirPath)) {
      const files = fs.readdirSync(dirPath);
      console.log(`✅ ${dir} (${files.length} 个文件)`);
    } else {
      console.log(`❌ ${dir} - 不存在`);
    }
  });
  
  // 检查服务器配置文件
  console.log('\n⚙️  检查服务器配置:');
  const configFiles = ['.htaccess', 'nginx.conf.example'];
  configFiles.forEach(file => {
    const filePath = path.join(distDir, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} - 存在`);
    } else {
      console.log(`❌ ${file} - 不存在`);
    }
  });
  
  console.log('\n📋 部署建议:');
  console.log('1. 确保将整个 xuanlan-mobile 目录上传到服务器');
  console.log('2. 访问页面前清除浏览器缓存 (Ctrl+Shift+R 或 Cmd+Shift+R)');
  console.log('3. 确保服务器支持 .htaccess 文件 (Apache) 或配置对应的 Nginx 规则');
  console.log('4. 检查服务器的 MIME 类型配置');
}

checkDeployment(); 
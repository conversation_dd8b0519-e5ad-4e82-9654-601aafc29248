{"name": "xuanlan-mobile", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev-test": "vite --mode prod", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build && node scripts/post-build.js", "build-only-test": "vite build --mode test && node scripts/post-build.js", "build-only-prod": "vite build --mode prod && node scripts/post-build.js", "deploy-check": "node scripts/deploy-check.js", "type-check": "vue-tsc --build", "yalc-update": "yalc remove --all && yalc add jb-mobile-ui && yalc update && rm -rf node_modules && pnpm i && npm run dev"}, "dependencies": {"@wecom/jssdk": "^2.3.1", "axios": "^1.9.0", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dom-to-image": "^2.6.0", "dom-to-image-more": "^3.6.0", "echarts": "^5.6.0", "jb-mobile-ui": "1.4.6", "jinbi-utils": "1.0.0-beta.1", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.3.0", "terser": "^5.39.2", "vant": "^4.9.19", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-virtual-scroller": "2.0.0-beta.8", "watermark-js-plus": "^1.6.2"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/preset-env": "^7.26.0", "@tsconfig/node22": "^22.0.1", "@types/lodash-es": "^4.17.12", "@types/node": "^22.14.0", "@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "fast-glob": "^3.3.3", "less": "^4.3.0", "npm-run-all2": "^7.0.2", "postcss-px-to-viewport": "^1.1.1", "typescript": "~5.8.0", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^5.4.19", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}